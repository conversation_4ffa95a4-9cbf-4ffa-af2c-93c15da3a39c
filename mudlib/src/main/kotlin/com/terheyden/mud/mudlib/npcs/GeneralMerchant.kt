package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.BreadLoaf
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.items.Rope
import com.terheyden.mud.mudlib.items.Torch
import com.terheyden.mud.mudlib.items.TravelPack
import com.terheyden.mud.mudlib.items.WaterBottle

/**
 * A general merchant who sells various useful items and supplies.
 */
class GeneralMerchant : NPC(
    id = "general_merchant",
    name = "general merchant",
    description = "A middle-aged merchant with keen eyes, practical clothes, and a leather apron full of pockets.",
    inventory = mutableListOf(
        HealingPotion(), HealingPotion(), HealingPotion(),
        BreadLoaf(), B<PERSON><PERSON>oa<PERSON>(),
        Gold<PERSON><PERSON><PERSON>(), <PERSON><PERSON><PERSON><PERSON>(),
        <PERSON><PERSON>(), <PERSON><PERSON>(),
        <PERSON><PERSON>(), <PERSON><PERSON>(), <PERSON><PERSON>(),
        TravelPack(),
        WaterBottle(), WaterBottle(),
    ),
    maxHealthPoints = 90,
    currentHealthPoints = 90,
    level = 3,
    baseAttackPower = 5,
    baseDefense = 8,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {

    private var hasIntroduced = false
    private var tradeCount = 0

    override fun getGreeting(player: Player): String {
        return if (!hasIntroduced) {
            hasIntroduced = true
            "The merchant looks up and smiles warmly. 'Welcome! I'm Marcus, and I've got everything an adventurer needs. What can I help you find?'"
        } else {
            "Marcus grins. 'Back again? What can I interest you in this time?'"
        }
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                getGreeting(player)
            }

            lowerMessage.contains("buy") || lowerMessage.contains("purchase") || lowerMessage.contains("trade") -> {
                tradeCount++
                when {
                    lowerMessage.contains("potion") || lowerMessage.contains("healing") -> {
                        "Marcus holds up a red vial. 'Healing potions! Essential for adventurers. Twenty gold pieces each.'"
                    }

                    lowerMessage.contains("food") || lowerMessage.contains("bread") -> {
                        "Marcus gestures to fresh loaves. 'Fresh bread from the village bakery! Perfect for journeys. Five gold pieces per loaf.'"
                    }

                    lowerMessage.contains("rope") -> {
                        "Marcus shows you sturdy rope. 'Good hemp rope, strong enough to hold a horse! Ten gold pieces for fifty feet.'"
                    }

                    lowerMessage.contains("torch") || lowerMessage.contains("light") -> {
                        "Marcus picks up a torch. 'These burn bright and long. Essential for dark places. Three gold pieces each.'"
                    }

                    lowerMessage.contains("pack") || lowerMessage.contains("bag") -> {
                        "Marcus shows you a travel pack. 'This will carry all your gear. Made by the local leatherworker. Thirty gold pieces.'"
                    }

                    lowerMessage.contains("water") || lowerMessage.contains("bottle") -> {
                        "Marcus holds up a water bottle. 'Clean water is life, friend. Eight gold pieces each.'"
                    }

                    else -> {
                        "Marcus spreads his arms wide. 'I've got potions, bread, rope, torches, packs, and water bottles. What do you need?'"
                    }
                }
            }

            lowerMessage.contains("sell") -> {
                "Marcus examines you appraisingly. 'I might be interested in buying certain " +
                        "items, depending on what you've got. Gems, rare materials, magical " +
                        "items - those sorts of things. What are you looking to sell?'"
            }

            lowerMessage.contains("price") || lowerMessage.contains("cost") || lowerMessage.contains("gold") -> {
                "Marcus counts on his fingers. 'Let's see... healing potions are twenty gold, " +
                        "bread is five gold, rope is ten gold, torches are three gold each, " +
                        "travel packs are thirty gold, and water bottles are eight gold. " +
                        "Fair prices for quality goods!'"
            }

            lowerMessage.contains("news") || lowerMessage.contains("information") -> {
                when (tradeCount % 3) {
                    0 -> "Marcus leans in conspiratorially. 'I've heard strange tales from " +
                            "travelers lately. Some speak of ancient ruins awakening, others " +
                            "of magical energies stirring in the old places. Exciting times " +
                            "for adventurers, I'd say!'"

                    1 -> "Marcus nods thoughtfully. 'The village has been busier than usual. " +
                            "More adventurers passing through, more demand for supplies. " +
                            "Something big is happening in the world, mark my words.'"

                    else -> "Marcus glances around and lowers his voice. 'Between you and me, " +
                            "I've been getting requests for some unusual items lately. Rare " +
                            "components, magical focuses, that sort of thing. The times are " +
                            "changing, friend.'"
                }
            }

            lowerMessage.contains("village") || lowerMessage.contains("town") -> {
                "Marcus gestures around the market. 'This is a good village, friend. Honest " +
                        "folk, fair prices, and always something interesting happening. The " +
                        "blacksmith makes fine weapons, the temple offers healing, and the " +
                        "inn serves the best ale for miles around.'"
            }

            lowerMessage.contains("goodbye") || lowerMessage.contains("farewell") -> {
                "Marcus waves cheerfully. 'Safe travels, friend! Come back anytime you need " +
                        "supplies. And remember - if you find anything interesting in your " +
                        "adventures, I might be interested in buying it!'"
            }

            else -> {
                val responses = listOf(
                    "Marcus nods attentively. 'Tell me more about that.'",
                    "Marcus strokes his chin thoughtfully. 'Interesting...'",
                    "Marcus's eyes light up with merchant's curiosity. 'Go on!'",
                    "Marcus leans forward with interest. 'That sounds profitable!'",
                    "Marcus chuckles and adjusts his apron. 'You adventurers always have the best stories.'"
                )
                responses.random()
            }
        }
    }
}
