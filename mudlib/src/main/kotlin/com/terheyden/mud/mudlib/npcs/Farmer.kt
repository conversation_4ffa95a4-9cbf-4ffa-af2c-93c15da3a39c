package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.BreadLoaf
import com.terheyden.mud.mudlib.items.Flour
import com.terheyden.mud.mudlib.items.GoldCoins

class Farmer : NPC(
    id = "farmer",
    name = "farmer",
    description = "A hardworking farmer who tends the fields around the village.",
    inventory = mutableListOf(BreadLoaf(), Flour(), GoldCoins()),
    maxHealthPoints = 90,
    currentHealthPoints = 90,
    level = 2,
    baseAttackPower = 6,
    baseDefense = 8,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {
    override fun getGreeting(player: Player): String {
        return "The farmer wipes dirt from his hands. 'Good day! Crops are growing well.'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        return "The farmer nods. 'Hard work and good weather make fine harvests.'"
    }
}
