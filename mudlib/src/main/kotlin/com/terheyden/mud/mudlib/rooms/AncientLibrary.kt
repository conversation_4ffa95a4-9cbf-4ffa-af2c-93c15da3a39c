package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.puzzle.PuzzleRoom
import com.terheyden.mud.corelib.puzzle.PuzzleState
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.AncientScroll
import com.terheyden.mud.mudlib.items.MagicalTome
import com.terheyden.mud.mudlib.puzzles.LibraryRiddlePuzzle
import org.springframework.stereotype.Component

/**
 * An ancient library filled with mystical knowledge and riddle-based puzzles.
 * Players must solve riddles posed by the magical books to unlock the library's secrets.
 */
@Component
class AncientLibrary : PuzzleRoom(
    id = "ancient_library",
    name = "Ancient Library",
    description = "A vast library with towering shelves stretching into shadows and floating candles providing " +
            "flickering light. A dark oak reading desk holds a glowing book in the center.",
    features = mutableListOf(
        RoomFeature(
            id = "towering_shelves",
            names = listOf("towering shelves", "shelves", "bookshelves", "tall shelves"),
            description = "Towering shelves carved from ancient wood reach impossibly high, " +
                    "filled with thousands of books and scrolls.",
            keywords = listOf("towering", "shelves", "ancient", "wood", "books", "scrolls"),
        ),
        RoomFeature(
            id = "floating_candles",
            names = listOf("floating candles", "candles", "magical candles", "flickering candles"),
            description = "Dozens of candles float freely through the air, " +
                    "casting warm golden light that follows you as you move.",
            keywords = listOf("floating", "candles", "golden", "light", "magical"),
        ),
        RoomFeature(
            id = "reading_desk",
            names = listOf("reading desk", "desk", "oak desk", "magnificent desk"),
            description = "A beautiful desk carved from dark oak " +
                    "with intricate vine patterns and brass-handled drawers.",
            keywords = listOf("desk", "oak", "carved", "patterns", "drawers", "brass"),
        ),
        RoomFeature(
            id = "glowing_book",
            names = listOf("glowing book", "book", "open book", "magical book", "riddle book"),
            description = "An ancient tome glows with blue-white light, " +
                    "its shifting text containing wisdom unlocked through riddles.",
            keywords = listOf("ancient", "tome", "glowing", "blue-white", "wisdom", "riddles"),
        ),
    ),
    exits = mutableMapOf(
        Direction.SOUTH to TowerChamber::class,
        Direction.EAST to RunicCircle::class,
    ),
    items = mutableListOf(
        // Items will be revealed when puzzle is solved
    ),
) {

    private val riddlePuzzle = LibraryRiddlePuzzle()

    override fun handlePuzzleInteraction(playerId: String, action: String, target: String): String? {
        return when (action.lowercase()) {
            "say", "answer", "speak" -> handleRiddleAnswer(target)
            "examine" -> when {
                target.contains("book") || target.contains("riddle") -> getCurrentRiddleText()
                else -> null
            }

            "use" -> when {
                target.contains("book") || target.contains("desk") -> getCurrentRiddleText()
                else -> null
            }

            else -> null
        }
    }

    private fun handleRiddleAnswer(answer: String): String {
        if (puzzleState == PuzzleState.SOLVED) {
            return "The riddles have already been solved. The library's secrets are now open to you."
        }

        val result = riddlePuzzle.checkAnswer(answer)

        return when (result) {
            LibraryRiddlePuzzle.PuzzleResult.CORRECT_CONTINUE -> {
                puzzleState = PuzzleState.IN_PROGRESS
                "Correct! The book glows brighter and the pages turn to reveal the next riddle.\n\n" +
                        getCurrentRiddleText()
            }

            LibraryRiddlePuzzle.PuzzleResult.COMPLETE -> {
                onPuzzleSolved("player")
                revealTreasures()
                "Excellent! You have answered all the riddles correctly. The book closes with a " +
                        "satisfied whisper, and you hear the sound of hidden compartments opening " +
                        "throughout the library. Ancient knowledge and treasures are now yours to discover!"
            }

            LibraryRiddlePuzzle.PuzzleResult.INCORRECT -> {
                "That is not correct. The book dims slightly, showing its disappointment. " +
                        "You have ${riddlePuzzle.getAttemptsRemaining()} attempts remaining. " +
                        "Would you like a hint? Try 'examine book' for guidance."
            }

            LibraryRiddlePuzzle.PuzzleResult.FAILED -> {
                onPuzzleFailed("player")
                riddlePuzzle.reset()
                "The book snaps shut with an angry flutter of pages. You have failed the test of " +
                        "wisdom. The riddles have reset - you may try again if you dare."
            }

            LibraryRiddlePuzzle.PuzzleResult.ALREADY_COMPLETE -> {
                "The riddles have already been completed."
            }
        }
    }

    private fun getCurrentRiddleText(): String {
        val riddle = riddlePuzzle.getCurrentRiddle()
        return if (riddle != null) {
            "The glowing book presents this riddle:\n\n${riddle.question}\n\n" +
                    "Speak your answer aloud. (Attempts: ${riddlePuzzle.getAttemptsRemaining()})"
        } else {
            "The book's wisdom has been unlocked."
        }
    }

    private fun revealTreasures() {
        // Add items that become available when puzzle is solved
        addItem(MagicalTome())
        addItem(AncientScroll())
    }

    override fun getSolvedDescription(): String {
        return "The library radiates with unlocked wisdom as hidden compartments reveal ancient treasures and scrolls."
    }

    override fun resetPuzzle() {
        super.resetPuzzle()
        riddlePuzzle.reset()
        // Remove revealed items
        removeItem("magical_tome")
        removeItem("ancient_scroll")
    }

    override fun onPlayerEnter(playerId: String) {
        // Provide initial guidance when entering
        if (puzzleState == PuzzleState.UNSOLVED) {
            // This could trigger a message about the glowing book
        }
    }
}
