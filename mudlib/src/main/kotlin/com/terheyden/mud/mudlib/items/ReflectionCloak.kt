package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A magical cloak that can bend light and reflections.
 */
class ReflectionCloak : Item(
    id = "reflection_cloak",
    name = "reflection cloak",
    description = "A shimmering cloak woven from captured moonbeams and mirror fragments that bends light around the wearer.",
    aliases = listOf("cloak", "shimmering cloak", "silver cloak", "magical cloak"),
    weight = 2,
    isPickupable = true
), Useable {

    private var isActive = false

    override fun use(user: Player): String {
        isActive = !isActive
        return if (isActive) {
            "You wrap the reflection cloak around yourself and feel its magic activate. " +
                    "Your form becomes harder to discern, shifting and shimmering like a " +
                    "mirage. You blend partially with your surroundings, gaining an advantage " +
                    "in avoiding detection."
        } else {
            "You deactivate the cloak's magic and become fully visible again. The shimmering " +
                    "effect fades, though the cloak still gleams with potential power."
        }
    }

    override fun getExamineDescription(): String {
        val statusDescription = if (isActive) {
            "The cloak is currently active, its magic bending light around you."
        } else {
            "The cloak is dormant, but you can sense its magical potential."
        }
        
        return super.getExamineDescription() + "\n\n$statusDescription"
    }
}
