package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.CloseLockContainer

/**
 * A mysterious treasure chest that may contain valuable items.
 */
class TreasureChest : CloseLockContainer(
    id = "treasure_chest",
    name = "an ornate treasure chest",
    description = "A beautifully crafted mahogany chest with brass fittings and intricate vine carvings.",
    aliases = listOf("ornate treasure chest", "chest", "treasure", "wooden chest", "ornate chest"),
    weight = 15,
    isPickupable = false,
    maxNumItems = 5,
    maxWeight = 50,
    items = mutableListOf(
        GoldCoins(),
        SilverRing(),
        HealingPotion(),
    ),
    closed = true,
)
