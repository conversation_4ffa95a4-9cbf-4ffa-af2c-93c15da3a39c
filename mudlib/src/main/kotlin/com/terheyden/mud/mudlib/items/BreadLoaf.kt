package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A fresh loaf of bread from the village bakery.
 */
class BreadLoaf : Item(
    id = "bread_loaf",
    name = "loaf of bread",
    description = "A fresh loaf of bread with a golden-brown crust that's still warm from the oven.",
    aliases = listOf("bread", "loaf", "food"),
    weight = 2,
    isPickupable = true,
), Useable {

    private var slicesRemaining = 8

    fun eat(): String {
        return if (slicesRemaining > 0) {
            slicesRemaining--
            when (slicesRemaining) {
                7 -> "You tear off a piece of warm bread. The crust is crispy and the inside soft and fluffy."

                in 4..6 -> "You eat another piece of delicious bread, feeling satisfied and energized."

                in 1..3 -> "You continue eating the bread. Only a few pieces left, but still fresh and tasty."

                0 -> "You eat the last piece of bread, savoring every crumb. You feel well-fed and satisfied."

                else -> "There's no bread left to eat."
            }
        } else {
            "There's no bread left - you've eaten the entire loaf."
        }
    }

    fun getSlicesRemaining(): Int = slicesRemaining

    fun hasFood(): Boolean = slicesRemaining > 0

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            when (slicesRemaining) {
                8 -> appendLine("The loaf is whole and warm from the oven.")
                in 5..7 -> appendLine("Partially eaten but most remains.")
                in 2..4 -> appendLine("About half the loaf remains.")
                1 -> appendLine("Only a small piece remains.")
                0 -> appendLine("Nothing but crumbs remain.")
            }
            if (hasFood()) {
                appendLine("You could eat some of this bread.")
            }
        }
    }

    override fun getShortDescription(): String {
        return when (slicesRemaining) {
            8 -> name
            in 5..7 -> "partially eaten bread loaf"
            in 2..4 -> "half-eaten bread loaf"
            1 -> "small piece of bread"
            0 -> "bread crumbs"
            else -> name
        }
    }

    override fun use(user: Player): String {
        if (hasFood()) {
            val result = eat()
            // Provide moderate health boost
            user.heal(10)
            return result
        } else {
            return "There's no bread left to eat."
        }
    }
}
