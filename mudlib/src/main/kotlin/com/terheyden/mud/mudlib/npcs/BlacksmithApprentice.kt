package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.HorseShoe
import com.terheyden.mud.mudlib.items.IronIngot

class BlacksmithApprentice : NPC(
    id = "blacksmith_apprentice",
    name = "blacksmith apprentice",
    description = "A young apprentice learning the art of blacksmithing.",
    inventory = mutableListOf(IronIngot(), HorseShoe()),
    maxHealthPoints = 80,
    currentHealthPoints = 80,
    level = 1,
    baseAttackPower = 8,
    baseDefense = 6,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {
    override fun getGreeting(player: Player): String {
        return "The apprentice looks up from the bellows. 'Hello! The master can help you better.'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        return "The apprentice nods eagerly. 'Still learning the trade!'"
    }
}
