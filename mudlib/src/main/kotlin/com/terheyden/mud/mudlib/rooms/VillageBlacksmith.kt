package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.IronIngot
import com.terheyden.mud.mudlib.items.LeatherArmor
import com.terheyden.mud.mudlib.npcs.BlacksmithApprentice
import com.terheyden.mud.mudlib.weapons.IronSword
import org.springframework.stereotype.Component
import com.terheyden.mud.mudlib.npcs.VillageBlacksmith as BlacksmithNPC

/**
 * A busy blacksmith shop where weapons and armor are forged and repaired.
 */
@Component
class VillageBlacksmith : Room(
    id = "village_blacksmith",
    name = "Village Blacksmith",
    description = "A hot, smoky blacksmith shop with the sound of hammer on anvil and sparks flying from the forge. Weapons and armor hang from the walls.",
    features = mutableListOf(
        RoomFeature(
            id = "roaring_forge",
            names = listOf("roaring forge", "forge", "fire", "furnace"),
            description = "A massive stone forge burns white-hot, fed by bellows that maintain perfect temperature for metalworking.",
            keywords = listOf("stone", "forge", "fire", "bellows", "metalworking"),
        ),
        RoomFeature(
            id = "anvil_workstation",
            names = listOf("anvil workstation", "anvil", "workstation", "iron anvil"),
            description = "A heavy iron anvil sits on a wooden stump with hammers, tongs, and smithing tools within easy reach.",
            keywords = listOf("iron", "anvil", "hammers", "tongs", "tools"),
        ),



        RoomFeature(
            id = "tool_collection",
            names = listOf("tool collection", "tools", "smithing tools", "blacksmith tools"),
            description = "An impressive collection of specialized tools hangs from hooks and " +
                    "rests in wooden holders - hammers of various sizes, tongs, files, " +
                    "punches, and other implements essential to the blacksmith's trade. " +
                    "Each tool shows signs of heavy use and careful maintenance.",
            keywords = listOf(
                "impressive",
                "collection",
                "specialized",
                "tools",
                "hangs",
                "hooks",
                "rests",
                "wooden",
                "holders",
                "hammers",
                "various",
                "sizes",
                "tongs",
                "files",
                "punches",
                "implements",
                "essential",
                "trade",
                "signs",
                "heavy",
                "use",
                "careful",
                "maintenance",
            ),
        ),
    ),
    exits = mutableMapOf(
        Direction.SOUTH to VillageMarket::class,
        Direction.EAST to VillageGuardPost::class,
    ),
    items = mutableListOf(
        BlacksmithNPC(),
        BlacksmithApprentice(),
        IronSword(),
        LeatherArmor(),
        IronIngot(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // Could add weapon repair services, custom forging, or apprenticeship opportunities
    }
}
