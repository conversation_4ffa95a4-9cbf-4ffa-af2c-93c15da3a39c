package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.items.MagicalPotion

/**
 * A mysterious potion seller who deals in magical brews and elixirs.
 */
class PotionSeller : NPC(
    id = "potion_seller",
    name = "potion seller",
    description = "A mysterious hooded figure tending a stall filled with bubbling vials and glowing potions.",
    inventory = mutableListOf(
        HealingPotion(), HealingPotion(), HealingPotion(),
        MagicalPotion(), MagicalPotion(),
        GoldCoins()
    ),
    maxHealthPoints = 80,
    currentHealthPoints = 80,
    level = 4,
    baseAttackPower = 6,
    baseDefense = 12,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {

    override fun getGreeting(player: Player): String {
        return "The hooded figure looks up from their cauldron. 'Greetings. I deal in potions and elixirs. What do you need?'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                getGreeting(player)
            }

            lowerMessage.contains("potion") || lowerMessage.contains("healing") -> {
                "The seller holds up a red vial. 'Healing potions are my specialty. Twenty-five gold pieces each.'"
            }

            lowerMessage.contains("magic") || lowerMessage.contains("enhancement") -> {
                "The seller's eyes glint beneath their hood. 'Enhancement potions boost abilities temporarily. Fifty gold pieces each.'"
            }

            else -> {
                "The potion seller nods mysteriously. 'Indeed... most interesting.'"
            }
        }
    }
}
