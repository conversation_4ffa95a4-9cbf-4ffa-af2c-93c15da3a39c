package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * An old rusty key with mysterious symbols.
 */
class RustyKey : Item(
    id = "rusty_key",
    name = "rusty key",
    description = "An old iron key, heavily rusted but functional, with strange symbols etched into its head.",
    weight = 1,
    isPickupable = true,
    aliases = listOf("key", "iron key", "old key")
) {

    /**
     * Custom examine description that mentions the symbols.
     */
    override fun getExamineDescription(): String {
        return super.getExamineDescription() + " The symbols match ancient magical runes found throughout this realm."
    }
}
