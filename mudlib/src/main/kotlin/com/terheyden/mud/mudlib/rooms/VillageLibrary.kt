package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.MapOfRegion
import com.terheyden.mud.mudlib.items.SkillBook
import com.terheyden.mud.mudlib.items.VillageHistory
import com.terheyden.mud.mudlib.npcs.ScholarlyVisitor
import com.terheyden.mud.mudlib.npcs.VillageLibrarian
import org.springframework.stereotype.Component

/**
 * A cozy village library filled with books, maps, and local knowledge.
 */
@Component
class VillageLibrary : Room(
    id = "village_library",
    name = "Village Library",
    description = "A cozy library filled with the scent of old books and parchment. Tall shelves line the walls while maps and reading tables fill the space.",
    features = mutableListOf(
        RoomFeature(
            id = "book_shelves",
            names = listOf("book shelves", "shelves", "wooden shelves", "tall shelves"),
            description = "Tall wooden shelves stretch from floor to ceiling, packed with well-organized books on history, geography, magic, and local lore.",
            keywords = listOf("tall", "wooden", "books", "history", "geography", "magic", "lore")
        ),

        RoomFeature(
            id = "wall_maps",
            names = listOf("wall maps", "maps", "charts", "regional maps"),
            description = "Large maps hang on the walls showing the local region, trade routes, and areas of historical significance.",
            keywords = listOf("maps", "charts", "region", "trade", "routes", "historical")
        ),

    ),
    exits = mutableMapOf(
        Direction.NORTH to VillageTemple::class,
    ),
    items = mutableListOf(
        VillageLibrarian(),
        ScholarlyVisitor(),
        VillageHistory(),
        MapOfRegion(),
        SkillBook(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // Could add book borrowing system, research quests, or skill learning
    }
}
