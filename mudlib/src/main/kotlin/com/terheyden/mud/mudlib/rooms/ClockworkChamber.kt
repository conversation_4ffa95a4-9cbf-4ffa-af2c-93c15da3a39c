package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.ClockworkKey
import com.terheyden.mud.mudlib.items.MechanicalDevice
import org.springframework.stereotype.Component

/**
 * A chamber filled with intricate clockwork mechanisms and mechanical puzzles.
 */
@Component
class ClockworkChamber : Room(
    id = "clockwork_chamber",
    name = "Clockwork Chamber",
    description = "A chamber filled with clockwork mechanisms where gears turn rhythmically in mechanical symphony. A massive clockwork device stands in the center with dials and levers.",
    features = mutableListOf(
        RoomFeature(
            id = "clockwork_gears",
            names = listOf("clockwork gears", "gears", "mechanical gears", "turning gears"),
            description = "Hundreds of brass and steel gears of various sizes turn in perfect synchronization, from coin-sized to wagon wheel-sized.",
            keywords = listOf("brass", "steel", "gears", "synchronization", "mechanical")
        ),
        RoomFeature(
            id = "brass_pipes",
            names = listOf("brass pipes", "pipes", "copper tubes", "tubes", "steam pipes"),
            description = "An elaborate network of brass pipes and copper tubes runs along walls and ceiling, carrying steam and glowing liquids.",
            keywords = listOf("brass", "pipes", "copper", "tubes", "steam")
        ),

        RoomFeature(
            id = "control_panel",
            names = listOf("control panel", "panel", "controls", "dials", "levers"),
            description = "A brass control panel with numerous dials, switches, and levers labeled with cryptic symbols.",
            keywords = listOf("brass", "control", "panel", "dials", "switches", "levers")
        ),
    ),
    exits = mutableMapOf(
        Direction.EAST to AlchemistLaboratory::class,
    ),
    items = mutableListOf(
        ClockworkKey(),
        MechanicalDevice(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The chamber could have mechanical puzzles or timing challenges
        // Could also serve as a place to repair or enhance mechanical items
    }
}
