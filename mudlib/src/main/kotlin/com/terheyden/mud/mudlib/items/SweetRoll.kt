package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A delicious sweet roll from the village bakery.
 */
class SweetRoll : Item(
    id = "sweet_roll",
    name = "sweet roll",
    description = "A golden-brown sweet roll still warm from the oven " +
            "with cinnamon sweetness and a glistening sugar glaze.",
    aliases = listOf("roll", "sweet", "pastry", "cinnamon roll"),
    weight = 1,
    isPickupable = true,
), Useable {

    private var hasBeenEaten = false

    override fun use(user: Player): String {
        return if (!hasBeenEaten) {
            hasBeenEaten = true
            user.heal(8)
            "You bite into the sweet roll, enjoying the soft pastry and cinnamon-sugar filling that lifts your spirits."
        } else {
            "You've already eaten the sweet roll. Only crumbs remain."
        }
    }

    override fun getExamineDescription(): String {
        return if (!hasBeenEaten) {
            super.getExamineDescription() + "\n\nThe sweet roll looks delicious. You could eat it."
        } else {
            "Only sweet crumbs remain of what was once a delicious sweet roll."
        }
    }

    override fun getShortDescription(): String {
        return if (hasBeenEaten) {
            "sweet roll crumbs"
        } else {
            name
        }
    }
}
