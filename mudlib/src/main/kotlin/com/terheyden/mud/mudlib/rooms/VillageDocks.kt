package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.FishingNet
import com.terheyden.mud.mudlib.items.FreshFish
import com.terheyden.mud.mudlib.items.WaterBottle
import com.terheyden.mud.mudlib.npcs.DockWorker
import com.terheyden.mud.mudlib.npcs.Fisherman
import org.springframework.stereotype.Component

/**
 * A busy dock area where fishing boats come and go, and goods are loaded and unloaded.
 */
@Component
class VillageDocks : Room(
    id = "village_docks",
    name = "Village Docks",
    description = "Wooden docks extending into a calm river where fishing boats are moored with nets hanging to dry. Workers load cargo while fishermen mend nets as water laps against the dock.",
    features = mutableListOf(
        RoomFeature(
            id = "wooden_docks",
            names = listOf("wooden docks", "docks", "pier", "wharf"),
            description = "The docks are built from thick wooden planks supported by sturdy " +
                    "posts driven deep into the riverbed. The wood is weathered but well-maintained, " +
                    "with iron fittings for mooring boats. The docks extend about fifty feet " +
                    "into the river, providing ample space for several boats.",
            keywords = listOf(
                "thick",
                "wooden",
                "planks",
                "supported",
                "sturdy",
                "posts",
                "driven",
                "deep",
                "riverbed",
                "weathered",
                "well-maintained",
                "iron",
                "fittings",
                "mooring",
                "extend",
                "fifty",
                "feet",
                "ample",
                "space"
            )
        ),
        RoomFeature(
            id = "fishing_boats",
            names = listOf("fishing boats", "boats", "vessels", "moored boats"),
            description = "Several small fishing boats are tied up at the docks. Each boat is " +
                    "equipped with nets, lines, and other fishing gear. The boats are painted " +
                    "in bright colors - blues, greens, and reds - and show signs of regular " +
                    "use and careful maintenance. Their names are painted on the sides in " +
                    "flowing script.",
            keywords = listOf(
                "small",
                "tied",
                "equipped",
                "nets",
                "lines",
                "fishing",
                "gear",
                "painted",
                "bright",
                "colors",
                "blues",
                "greens",
                "reds",
                "signs",
                "regular",
                "use",
                "careful",
                "maintenance",
                "names",
                "sides",
                "flowing",
                "script"
            )
        ),
        RoomFeature(
            id = "drying_nets",
            names = listOf("drying nets", "nets", "fishing nets", "hanging nets"),
            description = "Large fishing nets hang from wooden frames and boat masts, drying in " +
                    "the sun and breeze. The nets are woven from strong cord and show the " +
                    "skill of their makers. Some are being mended by fishermen who work with " +
                    "practiced hands to repair any tears or weak spots.",
            keywords = listOf(
                "large",
                "hang",
                "wooden",
                "frames",
                "boat",
                "masts",
                "drying",
                "sun",
                "breeze",
                "woven",
                "strong",
                "cord",
                "skill",
                "makers",
                "mended",
                "fishermen",
                "practiced",
                "hands",
                "repair",
                "tears",
                "weak",
                "spots"
            )
        ),
        RoomFeature(
            id = "cargo_crates",
            names = listOf("cargo crates", "crates", "boxes", "cargo"),
            description = "Wooden crates and barrels are stacked on the docks, waiting to be " +
                    "loaded onto boats or transported into the village. The containers hold " +
                    "various goods - some smell of spices, others contain the sound of " +
                    "clinking bottles or rustling grain. Each is marked with symbols " +
                    "indicating its contents and destination.",
            keywords = listOf(
                "wooden",
                "crates",
                "barrels",
                "stacked",
                "waiting",
                "loaded",
                "boats",
                "transported",
                "village",
                "containers",
                "various",
                "goods",
                "smell",
                "spices",
                "contain",
                "sound",
                "clinking",
                "bottles",
                "rustling",
                "grain",
                "marked",
                "symbols",
                "contents",
                "destination"
            )
        ),
        RoomFeature(
            id = "river_water",
            names = listOf("river water", "river", "water", "calm river"),
            description = "The river flows gently past the docks, its surface reflecting the sky " +
                    "and occasionally disturbed by fish jumping or boats passing. The water " +
                    "is clear enough to see the bottom near the shore, where small fish dart " +
                    "between the dock posts. The current is gentle but steady, carrying the " +
                    "boats of fishermen and traders to distant places.",
            keywords = listOf(
                "flows",
                "gently",
                "surface",
                "reflecting",
                "sky",
                "occasionally",
                "disturbed",
                "fish",
                "jumping",
                "boats",
                "passing",
                "clear",
                "bottom",
                "shore",
                "small",
                "dart",
                "between",
                "posts",
                "current",
                "steady",
                "carrying",
                "fishermen",
                "traders",
                "distant",
                "places"
            )
        ),
        RoomFeature(
            id = "fish_market_stall",
            names = listOf("fish market stall", "fish stall", "market stall", "fresh fish"),
            description = "A small market stall near the docks displays the day's catch on beds " +
                    "of ice. Fresh fish of various sizes and types are arranged attractively, " +
                    "their scales still glistening. The fishmonger calls out prices and " +
                    "extols the quality of his wares to passing customers.",
            keywords = listOf(
                "small",
                "market",
                "stall",
                "displays",
                "catch",
                "beds",
                "ice",
                "fresh",
                "fish",
                "various",
                "sizes",
                "types",
                "arranged",
                "attractively",
                "scales",
                "glistening",
                "fishmonger",
                "calls",
                "prices",
                "extols",
                "quality",
                "wares",
                "passing",
                "customers"
            )
        ),
    ),
    exits = mutableMapOf(
        Direction.WEST to VillageMarket::class,
        Direction.NORTH to VillageOutskirts::class,
    ),
    items = mutableListOf(
        Fisherman(),
        DockWorker(),
        FishingNet(),
        FreshFish(),
        WaterBottle(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // Could add fishing mini-game, boat travel, or trade opportunities
    }
}
