package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * An ancient scroll containing mysterious knowledge.
 */
class AncientScroll : Item(
    id = "ancient_scroll",
    name = "ancient scroll",
    description = "A yellowed parchment scroll tied with a faded ribbon. " +
            "You can see mysterious writing through the translucent material.",
    weight = 1,
    isPickupable = true,
    aliases = listOf("scroll", "parchment", "document", "writing")
) {

    private var isRead = false

    /**
     * Read the scroll to reveal its contents.
     */
    fun read(): String {
        isRead = true
        return "The scroll contains an ancient incantation written in flowing script: " +
                "'By the light of crystal pure, let the seeker's path endure. " +
                "Through shadow deep and forest wide, let wisdom be your faithful guide.' " +
                "The words seem to glow briefly as you read them."
    }

    /**
     * Check if the scroll has been read.
     */
    fun hasBeenRead(): Boolean = isRead

    /**
     * Get magical knowledge from the scroll.
     */
    fun getSpellKnowledge(): String? {
        return if (isRead) {
            "Light Guidance Spell"
        } else {
            null
        }
    }

    /**
     * Custom examine description that changes based on whether it's been read.
     */
    override fun getExamineDescription(): String {
        val baseDescription = super.getExamineDescription()
        return if (isRead) {
            "$baseDescription The scroll lies open, its mystical words revealed."
        } else {
            "$baseDescription The ribbon can be untied to read the contents."
        }
    }
}
