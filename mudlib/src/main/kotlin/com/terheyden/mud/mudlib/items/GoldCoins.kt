package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A pouch of gold coins - valuable currency.
 */
class GoldCoins : Item(
    id = "gold_coins",
    name = "pouch of gold coins",
    description = "A leather pouch filled with gleaming gold coins that clink musically when moved.",
    aliases = listOf("coins", "gold", "pouch", "money", "currency"),
    weight = 3,
    isPickupable = true,
) {

    private val coinCount = (10..50).random()

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("Contains approximately $coinCount gold coins.")
        }
    }

    fun getCoinCount(): Int = coinCount
}
