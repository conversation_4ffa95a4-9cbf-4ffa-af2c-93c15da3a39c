package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A powerful magical tome that can teach the reader ancient spells and knowledge.
 */
class MagicalTome : Item(
    id = "magical_tome",
    name = "tome of ancient wisdom",
    description = "A thick leather-bound book with glowing pages and shifting silver runes that hums with magical energy.",
    aliases = listOf("tome", "book", "magical book", "ancient tome", "wisdom tome"),
    weight = 5,
    isPickupable = true,
), Useable {

    private var timesRead = 0
    private val maxReadings = 3

    /**
     * Read the tome to gain magical knowledge.
     */
    fun read(): String {
        timesRead++
        return when (timesRead) {
            1 -> "As you open the tome, the pages flutter on their own, revealing secrets of " +
                    "elemental magic. You feel your understanding of the mystical arts deepen. " +
                    "The knowledge of basic fire magic fills your mind!"

            2 -> "The tome reveals more advanced secrets this time, showing you the intricate " +
                    "patterns of healing magic. You sense your magical abilities growing stronger. " +
                    "You have learned the art of magical healing!"

            3 -> "The final secrets of the tome unfold before you - the most powerful magic of " +
                    "protection and warding. Your magical education is now complete, and you " +
                    "feel the tome's power has been fully transferred to you."

            else -> "The tome's pages are now blank, its magical knowledge fully absorbed. " +
                    "It remains a beautiful book, but its power has been spent."
        }
    }

    /**
     * Check if the tome still has knowledge to impart.
     */
    fun hasKnowledge(): Boolean = timesRead < maxReadings

    /**
     * Get the current state of the tome.
     */
    fun getKnowledgeLevel(): String {
        return when (timesRead) {
            0 -> "The tome pulses with untapped magical knowledge."
            1 -> "The tome still holds significant magical secrets."
            2 -> "The tome contains one final, powerful secret."
            else -> "The tome's knowledge has been fully absorbed."
        }
    }

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine(getKnowledgeLevel())
            if (hasKnowledge()) {
                appendLine("You could try to 'read' this tome to learn from its wisdom.")
            }
        }
    }

    override fun use(user: Player): String {
        return if (hasKnowledge()) {
            // Could add actual stat bonuses here
            read()
        } else {
            "The tome's knowledge has already been absorbed."
        }
    }
}
