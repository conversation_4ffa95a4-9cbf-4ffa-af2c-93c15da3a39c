package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.items.SilverRing
import com.terheyden.mud.mudlib.npcs.GeneralMerchant
import com.terheyden.mud.mudlib.npcs.JewelryMerchant
import com.terheyden.mud.mudlib.npcs.PotionSeller
import org.springframework.stereotype.Component

/**
 * A bustling marketplace where villagers and travelers come to trade goods.
 */
@Component
class VillageMarket : Room(
    id = "village_market",
    name = "Village Market",
    description = "A busy marketplace with colorful stalls displaying produce, crafted items, potions, and jewelry. The air smells of spices, leather, and flowers.",
    features = mutableListOf(
        RoomFeature(
            id = "merchant_stalls",
            names = listOf("merchant stalls", "stalls", "booths", "market stalls"),
            description = "Wooden stalls with colorful awnings display wares on tables and in baskets while merchants call out prices.",
            keywords = listOf("wooden", "stalls", "awnings", "tables", "baskets", "merchants")
        ),




    ),
    exits = mutableMapOf(
        Direction.WEST to VillageSquare::class,
        Direction.NORTH to VillageBlacksmith::class,
        Direction.SOUTH to VillageBakery::class,
        Direction.EAST to VillageDocks::class,
    ),
    items = mutableListOf(
        GeneralMerchant(),
        PotionSeller(),
        JewelryMerchant(),
        GoldCoins(),
        HealingPotion(),
        SilverRing(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // Could add random market events, special sales, or merchant interactions
    }
}
