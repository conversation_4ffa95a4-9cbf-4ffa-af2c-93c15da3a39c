package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A stone charged with mystical power from the runic circle.
 */
class PowerStone : Item(
    id = "power_stone",
    name = "power stone",
    description = "A smooth obsidian stone that pulses with inner light and shifting runic symbols.",
    aliases = listOf("stone", "black stone", "runic stone", "obsidian stone"),
    weight = 2,
    isPickupable = true
), Useable {

    private var chargesRemaining = 3

    override fun use(user: Player): String {
        return if (chargesRemaining > 0) {
            chargesRemaining--
            "You focus through the power stone, feeling a surge of mystical energy! The symbols flare brightly and you feel empowered. (${chargesRemaining} charges remaining)"
        } else {
            "The power stone is drained of energy, its symbols faded to smooth obsidian. Perhaps it could be recharged at its source..."
        }
    }

    override fun getExamineDescription(): String {
        val chargeDescription = when (chargesRemaining) {
            3 -> "The stone pulses with full power, its runic symbols bright and active."
            2 -> "The stone still holds considerable power, though slightly dimmed."
            1 -> "The stone's power is waning, its symbols flickering weakly."
            else -> "The stone appears drained, its surface dark and lifeless."
        }
        
        return super.getExamineDescription() + "\n\n$chargeDescription"
    }
}
