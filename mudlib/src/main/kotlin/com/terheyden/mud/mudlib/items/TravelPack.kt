package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A sturdy travel pack for carrying adventuring gear.
 */
class TravelPack : Item(
    id = "travel_pack",
    name = "travel pack",
    description = "A well-crafted leather backpack with multiple compartments and padded straps for long journeys.",
    aliases = listOf("pack", "backpack", "bag", "leather pack"),
    weight = 5,
    isPickupable = true
), Useable {

    override fun use(user: Player): String {
        return "You examine the travel pack, checking compartments and testing straps. The craftsmanship is excellent and could carry substantial gear comfortably."
    }

    override fun getExamineDescription(): String {
        return super.getExamineDescription() + "\n\nHas a main compartment plus smaller pockets for organizing gear, with external loops for bulky items."
    }
}
