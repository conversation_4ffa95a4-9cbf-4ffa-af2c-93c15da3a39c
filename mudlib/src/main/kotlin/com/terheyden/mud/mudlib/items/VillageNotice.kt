package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A notice board posting with village news and information.
 */
class VillageNotice : Item(
    id = "village_notice",
    name = "village notice",
    description = "A parchment notice with clear writing containing important information for travelers and villagers.",
    aliases = listOf("notice", "parchment", "posting", "announcement"),
    weight = 1,
    isPickupable = true,
) {

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("=== VILLAGE NOTICE ===")
            appendLine()
            appendLine("TRAVELERS & ADVENTURERS:")
            appendLine()
            appendLine("• Inn: Rooms & meals available")
            appendLine("• WARNING: Strange lights at abandoned mine")
            appendLine("• Shop: New supplies from capital")
            appendLine("• Temple: Healing & blessings")
            appendLine("• MISSING: Three villagers near old mine")
            appendLine("• Reward for information")
            appendLine()
            appendLine("Contact Village Elder for details")
            appendLine()
            appendLine("- Village Council")
        }
    }
}
