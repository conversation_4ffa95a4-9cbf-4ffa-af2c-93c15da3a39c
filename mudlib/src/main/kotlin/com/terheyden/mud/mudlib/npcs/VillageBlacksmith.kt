package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.BlacksmithHammer
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.HorseShoe
import com.terheyden.mud.mudlib.items.IronIngot
import com.terheyden.mud.mudlib.items.LeatherArmor
import com.terheyden.mud.mudlib.weapons.IronSword

/**
 * A skilled blacksmith who forges weapons and armor for the village.
 */
class VillageBlacksmith : NPC(
    id = "village_blacksmith",
    name = "the village blacksmith",
    description = "A powerfully built man with massive forearms and hands blackened by years of metalwork.",
    inventory = mutableListOf(
        IronSword(), IronSword(),
        LeatherArmor(), LeatherArmor(),
        IronIngot(), IronIngot(), IronIngot(),
        BlacksmithHammer(),
        HorseShoe(), HorseShoe(),
        GoldCoins(), GoldCoins(),
    ),
    maxHealthPoints = 120,
    currentHealthPoints = 120,
    level = 4,
    baseAttackPower = 15, // Strong from smithing work
    baseDefense = 12,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {

    private var hasIntroduced = false
    private var repairCount = 0

    override fun getGreeting(player: Player): String {
        return if (!hasIntroduced) {
            hasIntroduced = true
            "The blacksmith looks up from his anvil. 'Well now, a customer! I'm Gareth, the village blacksmith. What can I do for you?'"
        } else {
            "Gareth grins and sets down his hammer. 'Back again? What brings you to my forge?'"
        }
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                getGreeting(player)
            }

            lowerMessage.contains("weapon") || lowerMessage.contains("sword") || lowerMessage.contains("blade") -> {
                "Gareth hefts a well-made iron sword. 'Fine work! Good steel, properly tempered. I've got swords, axes, maces - whatever you need. Fifty gold for a sword like this.'"
            }

            lowerMessage.contains("armor") || lowerMessage.contains("protection") -> {
                "Gareth gestures to the armor displays. 'Protection is just as important as " +
                        "a good weapon, friend. I craft leather armor that's flexible but tough, " +
                        "and I can work with mail and plate too, given the right materials. " +
                        "This leather armor here will turn aside claws and blades while letting " +
                        "you move freely. Forty gold pieces.'"
            }

            lowerMessage.contains("repair") || lowerMessage.contains("fix") || lowerMessage.contains("mend") -> {
                repairCount++
                "Gareth examines your equipment with a practiced eye. 'Aye, I can repair most " +
                        "anything made of metal or leather. Weapons lose their edge, armor gets " +
                        "dented and torn - it's the nature of an adventurer's life. Bring me " +
                        "what needs fixing and I'll make it good as new. Fair prices for " +
                        "honest work.'"
            }

            lowerMessage.contains("custom") || lowerMessage.contains("special") || lowerMessage.contains("commission") -> {
                "Gareth's eyes light up with interest. 'Custom work, eh? Now that's what I " +
                        "live for! Give me the right materials and enough time, and I can " +
                        "forge you something truly special. Enchanted metals, rare alloys, " +
                        "unique designs - I've done it all. What did you have in mind?'"
            }

            lowerMessage.contains("forge") || lowerMessage.contains("fire") || lowerMessage.contains("heat") -> {
                "Gareth glances proudly at his forge. 'This forge has been burning for twenty " +
                        "years without going cold. The fire burns hot enough to work any metal " +
                        "you can name. The secret is in the coal - I get the best from the " +
                        "mountains, and the bellows keep it at just the right temperature.'"
            }

            lowerMessage.contains("apprentice") || lowerMessage.contains("learn") || lowerMessage.contains("teach") -> {
                "Gareth chuckles and nods toward his apprentice. 'Young Tom there is learning " +
                        "the trade. Been with me two years now and he's got good hands for the " +
                        "work. Smithing isn't just about strength - it takes patience, skill, " +
                        "and an understanding of the metal. Not everyone has the calling.'"
            }

            lowerMessage.contains("materials") || lowerMessage.contains("metal") || lowerMessage.contains("iron") -> {
                "Gareth picks up an iron ingot, hefting its weight. 'Good materials make good " +
                        "weapons. I work mostly with iron and steel, but I can handle bronze, " +
                        "silver, even some of the more exotic metals if you can provide them. " +
                        "The quality of the raw material determines the quality of the finished " +
                        "product.'"
            }

            lowerMessage.contains("price") || lowerMessage.contains("cost") || lowerMessage.contains("gold") -> {
                "Gareth counts on his fingers. 'Let's see... iron swords are fifty gold, " +
                        "leather armor is forty gold, repairs depend on the damage but usually " +
                        "ten to twenty gold. Custom work varies by complexity and materials. " +
                        "Fair prices for quality craftsmanship!'"
            }

            lowerMessage.contains("village") || lowerMessage.contains("town") -> {
                "Gareth wipes his hands on his apron. 'This is a good village, friend. Honest " +
                        "folk who appreciate quality work. I shoe their horses, mend their " +
                        "tools, and arm their guards. Been part of this community my whole " +
                        "life, and I wouldn't have it any other way.'"
            }

            lowerMessage.contains("horseshoe") || lowerMessage.contains("horse") -> {
                "Gareth holds up a well-made horseshoe. 'Aye, I shoe horses too. Not as " +
                        "exciting as forging swords, but it's steady work and the horses " +
                        "appreciate good shoes. These are made to last and protect the " +
                        "hooves on rough ground.'"
            }

            lowerMessage.contains("goodbye") || lowerMessage.contains("farewell") -> {
                "Gareth raises his hammer in salute. 'Safe travels, friend! Remember - a " +
                        "well-maintained weapon could save your life someday. Don't neglect " +
                        "your gear, and come back if you need anything forged or repaired!'"
            }

            else -> {
                val responses = listOf(
                    "Gareth nods thoughtfully while examining a piece of hot metal. 'Interesting...'",
                    "Gareth wipes sweat from his brow and listens intently. 'Go on.'",
                    "Gareth sets down his hammer and gives you his full attention. 'Tell me more.'",
                    "Gareth's eyes show the interest of a craftsman. 'That sounds like quality work.'",
                    "Gareth chuckles, a deep rumbling sound. 'You adventurers always have the best stories.'",
                )
                responses.random()
            }
        }
    }
}
