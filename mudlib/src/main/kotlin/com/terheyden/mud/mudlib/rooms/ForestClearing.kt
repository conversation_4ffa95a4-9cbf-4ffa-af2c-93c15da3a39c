package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.corelib.room.StartingRoom
import com.terheyden.mud.mudlib.doors.GardenDoor
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.items.WoodenStick
import com.terheyden.mud.mudlib.npcs.WiseMerchant
import org.springframework.stereotype.Component

/**
 * The peaceful forest clearing where players begin their adventure.
 */
@Component
class ForestClearing : Room(
    id = "forest_clearing",
    name = "Forest Clearing",
    description = "A peaceful clearing surrounded by tall oak trees with sunlight filtering through the canopy. " +
            "A worn path leads north toward an old stone tower.",
    features = mutableListOf(
        RoomFeature(
            id = "oak_trees",
            names = listOf("oak trees", "trees", "oaks"),
            description = "Ancient oak trees tower above with thick, weathered trunks " +
                    "and interwoven branches forming a natural canopy.",
            keywords = listOf("oak", "trees", "trunks", "branches", "canopy"),
        ),
    ),
    exits = mutableMapOf(
        Direction.NORTH to TowerEntrance::class,
        Direction.SOUTH to ForestPath::class,
        Direction.EAST to CrystalCave::class,
        Direction.WEST to SecretGarden::class,
    ),
    items = mutableListOf(
        WoodenStick(),
        HealingPotion(),
        WiseMerchant(),
    ),
    doors = mutableListOf(GardenDoor(Direction.WEST)),
), StartingRoom {

    override fun onPlayerEnter(playerId: String) {
        // Could add special behavior when players enter, like:
        // - Random encounters
        // - Weather effects
        // - Time-based events
    }
}
