package com.terheyden.mud.mudlib.weapons

import com.terheyden.mud.corelib.weapon.Weapon
import com.terheyden.mud.corelib.weapon.WeaponType

/**
 * A sturdy iron sword suitable for combat.
 */
class IronSword : Weapon(
    id = "iron_sword",
    name = "iron sword",
    description = "A well-crafted iron sword with a sharp blade and leather-wrapped hilt that feels perfectly balanced.",
    aliases = listOf("sword", "blade", "iron blade"),
    weight = 5,
    damage = 15,
    weaponType = WeaponType.MELEE,
    durability = 150,
) {

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("The sword's crossguard bears intricate engravings, and the pommel is weighted for perfect balance.")
            if (isInGoodCondition()) {
                appendLine("The blade is sharp and ready for battle.")
            } else if (isBroken()) {
                appendLine("The blade is chipped and dull, barely suitable for combat.")
            }
        }
    }
}
