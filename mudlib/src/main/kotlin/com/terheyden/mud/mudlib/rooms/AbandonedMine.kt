package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.GoldNugget
import com.terheyden.mud.mudlib.npcs.CaveTroll
import com.terheyden.mud.mudlib.weapons.MiningPickaxe
import org.springframework.stereotype.Component

/**
 * An old mining operation, now home to dangerous creatures and forgotten treasures.
 */
@Component
class AbandonedMine : Room(
    id = "abandoned_mine",
    name = "Abandoned Mine",
    description = "An old mine shaft with creaking wooden support beams and rusted equipment scattered about. Cart tracks lead into the darkness while water drips in the stale air.",
    features = mutableListOf(
        RoomFeature(
            id = "wooden_supports",
            names = listOf("wooden supports", "support beams", "beams", "wooden beams"),
            description = "Thick wooden beams support the mine entrance, darkened with age and showing stress cracks that creak softly.",
            keywords = listOf("wooden", "beams", "support", "cracks", "creak")
        ),
        RoomFeature(
            id = "mining_equipment",
            names = listOf("mining equipment", "equipment", "rusted equipment", "tools"),
            description = "Old mining tools scattered throughout - broken pickaxes, rusted shovels, and dented buckets covered in dust.",
            keywords = listOf("mining", "tools", "pickaxes", "shovels", "buckets", "dust")
        ),
        RoomFeature(
            id = "cart_tracks",
            names = listOf("cart tracks", "tracks", "mine tracks", "rails"),
            description = "Rusted iron rails run along the mine floor with overturned carts lying beside the tracks.",
            keywords = listOf("iron", "rails", "tracks", "carts", "rusted")
        ),
        RoomFeature(
            id = "excavated_walls",
            names = listOf("excavated walls", "walls", "mine walls", "rock walls"),
            description = "Excavated walls show tool marks and blast patterns with mineral veins glinting through the rock.",
            keywords = listOf("walls", "excavated", "tool", "marks", "minerals", "rock")
        ),
    ),
    exits = mutableMapOf(
        Direction.WEST to DungeonChamber::class,
    ),
    items = mutableListOf(
        CaveTroll(),
        MiningPickaxe(),
        GoldNugget(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The mine could have dangerous effects
        // - Cave-ins and structural collapses
        // - Toxic gases
        // - Dangerous creatures
        // - Hidden treasure veins
    }
}
