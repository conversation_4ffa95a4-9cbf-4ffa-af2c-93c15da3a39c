package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.npcs.VillageGuard
import com.terheyden.mud.mudlib.weapons.IronSword
import org.springframework.stereotype.Component

/**
 * The village guard post where the local guards maintain order and security.
 */
@Component
class VillageGuardPost : Room(
    id = "village_guard_post",
    name = "Village Guard Post",
    description = "A stone building serving as the village guard headquarters with weapon racks lining the walls.",
    features = mutableListOf(
        RoomFeature(
            id = "weapon_racks",
            names = listOf("weapon racks", "racks", "weapons"),
            description = "Wooden racks hold various guard weapons.",
            keywords = listOf("wooden", "racks", "weapons", "guard"),
        ),
    ),
    exits = mutableMapOf(
        Direction.WEST to VillageOutskirts::class,
    ),
    items = mutableListOf(
        VillageGuard(),
        IronSword(),
        GoldCoins(),
    ),
)
