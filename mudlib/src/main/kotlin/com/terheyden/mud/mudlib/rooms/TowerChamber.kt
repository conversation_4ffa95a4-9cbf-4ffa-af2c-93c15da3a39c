package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.AncientScroll
import com.terheyden.mud.mudlib.items.CrystalOrb
import com.terheyden.mud.mudlib.weapons.IronSword
import org.springframework.stereotype.Component

/**
 * The mystical chamber at the top of the tower.
 */
@Component
class TowerChamber : Room(
    id = "tower_chamber",
    name = "Tower Chamber",
    description = "A circular chamber at the tower's peak with ancient books lining the walls and a glowing crystal orb on a central pedestal. Narrow windows reveal the forest stretching endlessly below.",
    features = mutableListOf(
        RoomFeature(
            id = "ancient_books",
            names = listOf("ancient books", "books", "tomes"),
            description = "Ancient tomes bound in leather and strange materials line the shelves, some glowing faintly with mysterious energy.",
            keywords = listOf("ancient", "tomes", "leather", "shelves", "glowing", "mysterious")
        ),
        RoomFeature(
            id = "crystal_orb_pedestal",
            names = listOf("pedestal", "stone pedestal", "crystal orb", "orb"),
            description = "A white marble pedestal holds a glowing crystal orb that radiates subtle warmth and magical energy.",
            keywords = listOf("marble", "pedestal", "crystal", "orb", "glowing", "magical")
        ),
    ),
    exits = mutableMapOf(
        Direction.DOWN to TowerEntrance::class,
        Direction.NORTH to AncientLibrary::class,
    ),
    items = mutableListOf(
        AncientScroll(),
        CrystalOrb(),
        IronSword(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The crystal orb could react to certain players or conditions
        // Could trigger magical events or provide visions
    }
}
