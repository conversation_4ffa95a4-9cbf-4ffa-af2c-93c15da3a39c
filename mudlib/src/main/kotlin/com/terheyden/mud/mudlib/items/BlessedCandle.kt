package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A blessed candle that provides light and spiritual comfort.
 */
class BlessedCandle : Item(
    id = "blessed_candle",
    name = "blessed candle",
    description = "A white wax candle blessed by the temple priest with holy symbols carved into the wax.",
    aliases = listOf("candle", "blessed", "holy candle", "sacred candle"),
    weight = 1,
    isPickupable = true
), Useable {

    private var isLit = false
    private var burnTimeRemaining = 120 // minutes

    override fun use(user: Player): String {
        return if (!isLit) {
            isLit = true
            "You light the blessed candle, which glows with warm golden light and fills you with peace and protection."
        } else {
            "You extinguish the blessed candle. The light fades but the blessing remains."
        }
    }

    override fun getExamineDescription(): String {
        val statusDescription = if (isLit) {
            "The candle burns with a steady, blessed flame that provides both light and comfort."
        } else {
            "The candle is unlit but ready to provide blessed illumination when needed."
        }
        
        return super.getExamineDescription() + "\n\n$statusDescription"
    }

    override fun getShortDescription(): String {
        return if (isLit) {
            "lit blessed candle"
        } else {
            name
        }
    }
}
