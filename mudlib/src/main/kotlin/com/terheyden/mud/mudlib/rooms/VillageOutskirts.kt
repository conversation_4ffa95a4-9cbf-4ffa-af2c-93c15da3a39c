package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.FreshFish
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.npcs.Farmer
import org.springframework.stereotype.Component

/**
 * The outskirts of the village where farms and countryside begin.
 */
@Component
class VillageOutskirts : Room(
    id = "village_outskirts",
    name = "Village Outskirts",
    description = "The village edge where buildings give way to farmland stretching into the distance. The air is fresh and clean.",
    features = mutableListOf(
        RoomFeature(
            id = "crop_fields",
            names = listOf("crop fields", "fields", "crops", "farmland"),
            description = "Well-tended fields stretch into the distance with various crops.",
            keywords = listOf("fields", "crops", "farmland")
        ),
    ),
    exits = mutableMapOf(
        Direction.SOUTH to VillageDocks::class,
        Direction.EAST to VillageGuardPost::class,
    ),
    items = mutableListOf(
        Farmer(),
        FreshFish(),
        GoldCoins(),
    ),
)
