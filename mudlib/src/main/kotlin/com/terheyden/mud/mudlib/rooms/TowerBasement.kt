package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.MagicalTome
import com.terheyden.mud.mudlib.items.TreasureChest
import org.springframework.stereotype.Component

/**
 * The basement beneath the ancient tower, filled with forgotten magical artifacts.
 */
@Component
class TowerBasement : Room(
    id = "tower_basement",
    name = "Tower Basement",
    description = "A circular stone chamber beneath the tower, filled with dusty shelves of forgotten tomes and alchemical equipment. The air smells of old parchment, herbs, and magic.",
    features = mutableListOf(
        RoomFeature(
            id = "dusty_shelves",
            names = listOf("dusty shelves", "shelves", "forgotten tomes", "tomes"),
            description = "Stone shelves hold ancient books, scrolls, and mysterious objects that hum with residual magical energy.",
            keywords = listOf("stone", "ancient", "books", "scrolls", "magical", "energy")
        ),
        RoomFeature(
            id = "alchemical_table",
            names = listOf("wooden table", "table", "alchemical equipment", "equipment"),
            description = "A large oak table covered with glass vials, copper tubes, scales, and a cauldron, with magical diagrams carved into the wood.",
            keywords = listOf("oak", "table", "vials", "cauldron", "diagrams", "magical")
        ),

    ),
    exits = mutableMapOf(
        Direction.UP to TowerEntrance::class,
        Direction.NORTH to UndergroundTunnel::class,
        Direction.WEST to AlchemistLaboratory::class,
    ),
    items = mutableListOf(
        MagicalTome(),
        TreasureChest(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The basement could have special magical effects
        // - Temporary magical knowledge buffs
        // - Chance to learn new abilities
        // - Magical item identification
    }
}
