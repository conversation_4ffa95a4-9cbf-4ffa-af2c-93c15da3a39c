package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A powerful elixir that enhances wisdom and magical understanding.
 */
class ElixirOfWisdom : Item(
    id = "elixir_of_wisdom",
    name = "Eli<PERSON>r of Wisdom",
    description = "A crystal vial containing golden liquid that glows with inner light and swirls with glimpses of ancient knowledge.",
    aliases = listOf("elixir", "wisdom elixir", "golden elixir", "vial", "potion"),
    weight = 1,
    isPickupable = true
), Useable {

    private var hasBeenUsed = false

    override fun use(user: Player): String {
        return if (!hasBeenUsed) {
            hasBeenUsed = true
            "You drink the Elixir of Wisdom in one smooth gulp. The golden liquid tastes like " +
                    "honey and starlight, and as it flows down your throat, you feel your mind " +
                    "expanding. Ancient knowledge flows through your consciousness, and for a " +
                    "moment, you understand the deeper mysteries of the world. The empty vial " +
                    "crumbles to crystal dust in your hands, its purpose fulfilled."
        } else {
            "The vial is empty - its wisdom has already been consumed."
        }
    }

    override fun getExamineDescription(): String {
        return if (!hasBeenUsed) {
            super.getExamineDescription() + "\n\nThe elixir swirls faster as you gaze at it, promising great wisdom but only once."
        } else {
            "An empty crystal vial that once held the Elixir of Wisdom, still carrying a faint glow."
        }
    }
}
