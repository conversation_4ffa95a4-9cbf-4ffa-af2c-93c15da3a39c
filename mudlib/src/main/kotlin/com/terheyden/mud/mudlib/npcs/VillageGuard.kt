package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.LeatherArmor
import com.terheyden.mud.mudlib.weapons.IronSword

class VillageGuard : NPC(
    id = "village_guard",
    name = "village guard",
    description = "A vigilant guard who protects the village from threats.",
    inventory = mutableListOf(IronSword(), LeatherArmor(), GoldCoins()),
    maxHealthPoints = 110,
    currentHealthPoints = 110,
    level = 4,
    baseAttackPower = 12,
    baseDefense = 10,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {
    override fun getGreeting(player: Player): String {
        return "The guard nods. 'Greetings, traveler. I keep the peace here.'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        return "The guard listens. 'I protect this village and its people.'"
    }
}
