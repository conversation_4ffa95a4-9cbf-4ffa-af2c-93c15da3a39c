package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A mysterious key that can open many locks.
 */
class SkeletonKey : Item(
    id = "skeleton_key",
    name = "skeleton key",
    description = "An ornate brass key with a shifting design that hums with magical energy and feels warm to the touch.",
    aliases = listOf("key", "brass key", "magical key", "ornate key"),
    weight = 1,
    isPickupable = true,
) {

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("Bears powerful enchantment that could unlock many secrets, but magic always has consequences.")
        }
    }
}
