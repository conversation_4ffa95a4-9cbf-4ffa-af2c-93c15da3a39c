package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.GoldNugget
import com.terheyden.mud.mudlib.weapons.MiningPickaxe

/**
 * A massive cave troll that has claimed the abandoned mine as its territory.
 */
class CaveTroll : NPC(
    id = "cave_troll",
    name = "cave troll",
    description = "A massive eight-foot creature with gray-green stone-like skin, tree-trunk arms, and malicious beady eyes.",
    maxHealthPoints = 150,
    currentHealthPoints = 150,
    level = 5,
    baseAttackPower = 22,
    baseDefense = 12,
    npcType = NPCType.AGGRESSIVE,
    experienceReward = 60,
    lootTable = listOf(GoldNugget(), MiningPickaxe()),
) {

    override fun getGreeting(player: Player): String {
        return "The cave troll roars with fury, beating its fists against its chest!"
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("gold") || lowerMessage.contains("treasure") -> {
                "The troll's eyes narrow with greed and it growls possessively, 'MINE! ALL MINE!'"
            }
            lowerMessage.contains("food") || lowerMessage.contains("eat") -> {
                "The troll licks its lips with a massive tongue and rumbles, 'YOU LOOK TASTY!'"
            }
            lowerMessage.contains("leave") || lowerMessage.contains("go") -> {
                "The troll blocks your path with its massive bulk and snarls, 'NO LEAVE! TROLL'S CAVE!'"
            }
            else -> {
                val responses = listOf(
                    "The cave troll grunts and beats its chest menacingly.",
                    "The troll's small eyes glitter with malicious intelligence.",
                    "The massive creature stamps its feet, making the ground shake.",
                    "The troll bares its jagged teeth in what might be a grin or a threat.",
                    "The cave troll roars, the sound echoing through the mine tunnels."
                )
                responses.random()
            }
        }
    }

    override fun onPlayerEnter(player: Player): String {
        return "The ground shakes as a massive cave troll emerges from the mine depths! It towers over you and roars a challenge that echoes through the tunnels!"
    }

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("This ancient creature has made the abandoned mine its home, hoarding treasures and fighting fiercely to protect its domain.")
        }
    }
}
