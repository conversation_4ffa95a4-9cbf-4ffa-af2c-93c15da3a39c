package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A fresh fish caught from the village river.
 */
class FreshFish : Item(
    id = "fresh_fish",
    name = "fresh fish",
    description = "A beautiful fresh fish with silver scales that glisten with river water.",
    aliases = listOf("fish", "fresh", "river fish", "catch"),
    weight = 2,
    isPickupable = true
), Useable {

    private var hasBeenEaten = false

    override fun use(user: Player): String {
        return if (!hasBeenEaten) {
            hasBeenEaten = true
            user.heal(12)
            "You prepare and eat the fresh fish. The meat is tender and flaky, with a " +
                    "clean, mild flavor that speaks of pure river water. It's incredibly " +
                    "fresh and satisfying, providing excellent nourishment. This is clearly " +
                    "the catch of an experienced fisherman!"
        } else {
            "You've already eaten the fish. Only bones remain."
        }
    }

    override fun getExamineDescription(): String {
        return if (!hasBeenEaten) {
            super.getExamineDescription() + "\n\nThe fish looks incredibly fresh and would make an excellent meal."
        } else {
            "Only clean fish bones remain - evidence of a good meal."
        }
    }

    override fun getShortDescription(): String {
        return if (hasBeenEaten) {
            "fish bones"
        } else {
            name
        }
    }
}
