package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A magical flower blessed by forest spirits.
 */
class EnchantedFlower : Item(
    id = "enchanted_flower",
    name = "enchanted flower",
    description = "A beautiful flower that glows with inner light, its petals shifting through impossible colors and emanating natural magic.",
    aliases = listOf("flower", "magical flower", "glowing flower", "blessed flower"),
    weight = 1,
    isPickupable = true,
) {

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("This flower was blessed by a forest spirit and carries powerful natural magic.")
            appendLine("It might provide protection or enhancement when carried.")
        }
    }
}
