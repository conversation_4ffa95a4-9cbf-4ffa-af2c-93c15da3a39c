package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.BlessedCandle
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.items.PrayerBook
import com.terheyden.mud.mudlib.npcs.TempleAcolyte
import com.terheyden.mud.mudlib.npcs.TemplePriest
import org.springframework.stereotype.Component

/**
 * A peaceful temple dedicated to healing and spiritual guidance.
 */
@Component
class VillageTemple : Room(
    id = "village_temple",
    name = "Village Temple",
    description = "A peaceful temple filled with golden light from stained glass windows and the scent of incense. Wooden pews face a marble altar adorned with candles and flowers.",
    features = mutableListOf(
        RoomFeature(
            id = "stained_glass_windows",
            names = listOf("stained glass windows", "windows", "colored windows", "glass windows"),
            description = "Beautiful stained glass windows depict scenes of divine healing and cast rainbow patterns across the temple floor.",
            keywords = listOf("stained", "glass", "windows", "healing", "rainbow", "divine")
        ),
        RoomFeature(
            id = "temple_altar",
            names = listOf("temple altar", "altar", "sacred altar", "holy altar"),
            description = "A white marble altar adorned with candles, flowers, and sacred symbols radiates gentle healing energy.",
            keywords = listOf("marble", "altar", "candles", "flowers", "sacred", "healing")
        ),

    ),
    exits = mutableMapOf(
        Direction.EAST to VillageSquare::class,
        Direction.SOUTH to VillageLibrary::class,
    ),
    items = mutableListOf(
        TemplePriest(),
        TempleAcolyte(),
        HealingPotion(),
        BlessedCandle(),
        PrayerBook(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // Could add blessing effects, healing aura, or spiritual guidance
    }
}
