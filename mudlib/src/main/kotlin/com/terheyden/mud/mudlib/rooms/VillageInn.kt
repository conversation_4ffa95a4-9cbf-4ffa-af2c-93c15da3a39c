package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.Ale
import com.terheyden.mud.mudlib.items.BreadLoaf
import com.terheyden.mud.mudlib.npcs.Innkeeper
import org.springframework.stereotype.Component

/**
 * A cozy village inn where travelers can rest and share stories.
 */
@Component
class VillageInn : Room(
    id = "village_inn",
    name = "The Prancing Pony Inn",
    description = "A warm inn buzzes with laughter and conversation around a crackling fireplace. The air smells of roasted meat, bread, and ale.",
    features = mutableListOf(
        RoomFeature(
            id = "stone_fireplace",
            names = listOf("stone fireplace", "fireplace", "crackling fire", "fire"),
            description = "A large stone fireplace dominates one wall with a cheerful fire and a painting of a prancing pony above the mantle.",
            keywords = listOf("stone", "fire", "mantle", "pony", "painting")
        ),

        RoomFeature(
            id = "inn_bar",
            names = listOf("bar", "inn bar", "wooden bar", "counter"),
            description = "A long wooden bar lined with bottles and kegs serves as the heart of the inn's social activity.",
            keywords = listOf("wooden", "bar", "bottles", "kegs", "social")
        ),
    ),
    exits = mutableMapOf(
        Direction.NORTH to VillageSquare::class,
        Direction.EAST to VillageBakery::class,
    ),
    items = mutableListOf(
        Innkeeper(),
        Ale(),
        BreadLoaf(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The inn could offer special services
        // - Rest and healing
        // - Information gathering
        // - Room rental
        // - Special events and entertainment
    }
}
