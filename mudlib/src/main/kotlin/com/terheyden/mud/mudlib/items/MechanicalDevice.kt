package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A portable mechanical device with unknown functions.
 */
class MechanicalDevice : Item(
    id = "mechanical_device",
    name = "mechanical device",
    description = "A compact mechanical device made of brass, steel, and crystal that hums quietly with internal energy.",
    aliases = listOf("device", "mechanical", "brass device", "calculating device"),
    weight = 3,
    isPickupable = true
), Useable {

    private var isActivated = false
    private var energyLevel = 100

    override fun use(user: Player): String {
        return if (energyLevel > 0) {
            isActivated = !isActivated
            energyLevel -= 10
            
            if (isActivated) {
                "You activate the mechanical device and it springs to life! Gears whir, dials " +
                        "spin, and crystal indicators glow brightly. The device begins performing " +
                        "complex calculations, measuring environmental factors you can't even " +
                        "perceive. Energy remaining: ${energyLevel}%"
            } else {
                "You deactivate the mechanical device. The gears slow to a stop and the " +
                        "indicators dim, but you can still feel the potential energy within. " +
                        "Energy remaining: ${energyLevel}%"
            }
        } else {
            "The mechanical device is out of energy. It needs to be recharged or wound up " +
                    "before it can function again."
        }
    }

    override fun getExamineDescription(): String {
        val statusDescription = when {
            energyLevel <= 0 -> "The device appears to be completely drained of energy."
            isActivated -> "The device is currently active, its mechanisms working busily."
            else -> "The device is dormant but ready for activation."
        }
        
        return super.getExamineDescription() + "\n\n$statusDescription Energy level: ${energyLevel}%"
    }
}
