package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A powerful crystal infused with elemental energies.
 */
class ElementalCrystal : Item(
    id = "elemental_crystal",
    name = "elemental crystal",
    description = "A magnificent crystal that pulses with all five elemental energies swirling in mesmerizing patterns.",
    aliases = listOf("crystal", "elemental", "power crystal", "magical crystal"),
    weight = 3,
    isPickupable = true
), Useable {

    override fun use(user: Player): String {
        return "You hold the crystal aloft, feeling elemental power flow through you and deepening your magical understanding."
    }

    override fun getExamineDescription(): String {
        return super.getExamineDescription() + "\n\nThe crystal responds to your presence, energies swirling faster as a powerful magical focus."
    }
}
