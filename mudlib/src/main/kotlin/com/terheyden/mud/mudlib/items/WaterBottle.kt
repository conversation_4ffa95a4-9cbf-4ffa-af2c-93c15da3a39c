package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A leather water bottle for carrying clean drinking water.
 */
class WaterBottle : Item(
    id = "water_bottle",
    name = "water bottle",
    description = "A well-crafted leather water bottle with a cork stopper, designed to keep water cool and fresh during long journeys.",
    aliases = listOf("bottle", "water", "leather bottle", "drink"),
    weight = 3,
    isPickupable = true
), Useable {

    private var drinksRemaining = 8

    override fun use(user: Player): String {
        return if (drinksRemaining > 0) {
            drinksRemaining--
            user.heal(3)
            
            when (drinksRemaining) {
                7, 6 -> "You take a refreshing drink from the water bottle. The water is " +
                        "cool and clean, quenching your thirst perfectly. The bottle " +
                        "still feels quite full."
                        
                5, 4 -> "You drink more of the clean water. It's incredibly refreshing " +
                        "and helps restore your energy. The bottle is about half full now."
                        
                3, 2 -> "You continue drinking from the water bottle. The water tastes " +
                        "pure and clean. You're getting close to the bottom of the bottle."
                        
                1 -> "You take another drink, leaving just a small amount of water " +
                        "in the bottle. You should probably refill it soon."
                        
                0 -> "You drink the last of the water from the bottle. It's now empty " +
                        "and will need to be refilled before you can drink from it again."
                        
                else -> "The bottle is empty."
            }
        } else {
            "The water bottle is empty. You'll need to refill it at a well or clean water source."
        }
    }

    override fun getExamineDescription(): String {
        val statusDescription = when (drinksRemaining) {
            8 -> "The bottle is completely full of clean water."
            6, 7 -> "The bottle is mostly full."
            4, 5 -> "The bottle is about half full."
            2, 3 -> "The bottle has only a small amount of water left."
            1 -> "The bottle has just a sip of water remaining."
            0 -> "The bottle is empty and needs to be refilled."
            else -> "The bottle's water level is unclear."
        }
        
        return super.getExamineDescription() + "\n\n$statusDescription"
    }

    override fun getShortDescription(): String {
        return when (drinksRemaining) {
            8 -> "full water bottle"
            in 5..7 -> "mostly full water bottle"
            in 2..4 -> "half-empty water bottle"
            1 -> "nearly empty water bottle"
            0 -> "empty water bottle"
            else -> name
        }
    }
}
