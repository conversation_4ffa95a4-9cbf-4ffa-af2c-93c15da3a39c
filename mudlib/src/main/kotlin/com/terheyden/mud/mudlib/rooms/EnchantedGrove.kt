package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.EnchantedFlower
import com.terheyden.mud.mudlib.items.MagicalBerries
import com.terheyden.mud.mudlib.npcs.ForestSpirit
import org.springframework.stereotype.Component

/**
 * A mystical grove where nature magic runs strong and ancient spirits dwell.
 */
@Component
class EnchantedGrove : Room(
    id = "enchanted_grove",
    name = "Enchanted Grove",
    description = "A magical grove where ancient silver-barked trees glow softly and colorful flowers bloom year-round. Translucent butterflies flutter between trees, leaving sparkling trails.",
    features = mutableListOf(
        RoomFeature(
            id = "silver_trees",
            names = listOf("silver trees", "trees", "ancient trees", "silver bark"),
            description = "Ancient trees with bark that gleams like silver and leaves that emit gentle light.",
            keywords = listOf("ancient", "silver", "bark", "light", "trees")
        ),
    ),
    exits = mutableMapOf(
        Direction.EAST to SecretGarden::class,
    ),
    items = mutableListOf(
        ForestSpirit(),
        MagicalBerries(),
        EnchantedFlower(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The grove could have powerful magical effects
        // - Healing and restoration
        // - Magical ability enhancement
        // - Communication with nature spirits
        // - Temporary magical transformations
    }
}
