package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A valuable gold nugget found in the mines.
 */
class GoldNugget : Item(
    id = "gold_nugget",
    name = "gold nugget",
    description = "A rough chunk of pure gold embedded with bits of quartz that gleams with warm luster.",
    aliases = listOf("nugget", "gold", "chunk", "ore"),
    weight = 4,
    isPickupable = true,
) {

    private val value = (50..150).random()

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("Worth approximately $value gold pieces.")
        }
    }

    fun getValue(): Int = value
}
