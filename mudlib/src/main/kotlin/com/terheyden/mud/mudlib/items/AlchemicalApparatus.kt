package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A set of precision alchemical tools for advanced potion making.
 */
class AlchemicalApparatus : Item(
    id = "alchemical_apparatus",
    name = "alchemical apparatus",
    description = "A beautifully crafted set of alchemical tools made from silver and crystal that hum with subtle magical energy.",
    aliases = listOf("apparatus", "tools", "alchemical tools", "silver tools", "alchemy set"),
    weight = 5,
    isPickupable = true
), Useable {

    override fun use(user: Player): String {
        return "You examine the alchemical apparatus, feeling your understanding of alchemical principles deepen. With tools like these, you could create potions of incredible potency."
    }

    override fun getExamineDescription(): String {
        return super.getExamineDescription() + "\n\n" +
                "Each tool in the set bears tiny runic inscriptions that seem to enhance " +
                "their effectiveness. The silver gleams with an inner light, and the crystal " +
                "components are flawlessly clear. This apparatus would be invaluable to any " +
                "serious practitioner of the alchemical arts."
    }
}
