package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A magical healing potion that can restore health.
 */
class HealingPotion : Item(
    id = "healing_potion",
    name = "healing potion",
    description = "A small glass vial filled with shimmering red liquid that glows faintly.",
    weight = 1,
    isPickupable = true,
    aliases = listOf("potion", "vial", "bottle", "red potion", "health potion")
) {

    companion object {
        const val HEALING_AMOUNT = 25
        const val IS_USABLE = true
    }

    /**
     * Amount of health this potion restores.
     */
    fun getHealingAmount(): Int = HEALING_AMOUNT

    /**
     * Check if the potion is still usable (not consumed).
     */
    fun isUsable(): Boolean = IS_USABLE

    /**
     * Use the potion (would typically remove it from inventory).
     */
    fun use(): String {
        return "You drink the healing potion, feeling warm liquid heal your wounds and restore vitality."
    }

    /**
     * Custom examine description that hints at its magical properties.
     */
    override fun getExamineDescription(): String {
        return super.getExamineDescription() + " The liquid pulses with inner light and magical energy."
    }
}
