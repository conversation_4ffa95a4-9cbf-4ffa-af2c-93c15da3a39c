#!/bin/bash

# Test script to verify the game is working properly
echo "Testing Spring MUD modular architecture..."
echo "=========================================="

# Build the project
echo "Building project..."
./gradlew build -q

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"

# Run the exit test
echo "Running exit test..."
./gradlew :driver:test --tests ExitTest -q

if [ $? -ne 0 ]; then
    echo "❌ Exit test failed!"
    exit 1
fi

echo "✅ Exit test passed!"

echo ""
echo "🎉 All tests passed! The modular architecture is working correctly."
echo ""
echo "To run the game interactively:"
echo "  ./run-game.sh"
echo ""
echo "Or directly:"
echo "  java -jar driver/build/libs/driver-0.0.1-SNAPSHOT.jar"
echo ""
echo "Module structure:"
echo "  📦 corelib  - Core interfaces and base classes"
echo "  🎮 driver   - Spring Boot application and game driver"
echo "  🌍 mudlib   - Default game world content"
