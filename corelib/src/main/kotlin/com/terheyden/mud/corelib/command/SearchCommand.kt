package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to search the room and show what can be examined.
 */
@Component
class SearchCommand : Command {
    override val name = "search"
    override val aliases = listOf("scan", "observe")
    override val description = "Search the room for things you can examine"

    override fun execute(player: Player, arg: String?): CommandResult {
        val currentRoom = player.getCurrentRoom()
        val examinableThings = mutableListOf<String>()

        // Add items
        if (!currentRoom.isEmpty()) {
            examinableThings.addAll(currentRoom.items.map { it.name })
        }

        // Add room features
        if (!currentRoom.features.isEmpty()) {
            examinableThings.addAll(currentRoom.features.getExaminableThings())
        }

        return if (examinableThings.isEmpty()) {
            CommandResult("You don't see anything particularly interesting to examine here.")
        } else {
            CommandResult("You can examine: ${examinableThings.joinToString(", ")}")
        }
    }
}
