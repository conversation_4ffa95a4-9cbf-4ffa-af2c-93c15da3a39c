package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to display help information.
 */
@Component
class HelpCommand(
    private val allCommands: List<Command>,
) : Command {
    override val name = "help"
    override val aliases = listOf("?", "commands")
    override val description = "Show available commands"

    override fun execute(player: Player, arg: String?): CommandResult {
        val message = buildString {
            appendLine("=== Available Commands ===")
            appendLine()

            // Group commands by their primary function
            val commandGroups = mapOf(
                "Movement" to listOf("north", "south", "east", "west", "up", "down"),
                "Exploration" to listOf("look", "examine", "search", "who"),
                "Items" to listOf("take", "drop", "inventory", "use"),
                "Combat" to listOf("attack", "equip", "status"),
                "Social" to listOf("say"),
                "Utility" to listOf("rest"),
                "System" to listOf("help", "quit", "set", "tick"),
            )

            commandGroups.forEach { (category, commandNames) ->
                appendLine("$category:")
                commandNames.forEach { commandName ->
                    val command = allCommands.find { it.name == commandName || commandName in it.aliases }
                    if (command != null) {
                        val aliases = if (command.aliases.isNotEmpty()) {
                            " (${command.aliases.joinToString(", ")})"
                        } else ""
                        appendLine("  ${command.name}$aliases - ${command.description}")
                    }
                }
                appendLine()
            }

            appendLine("You can use the full command name or any of its aliases.")
            appendLine("For movement, you can just type the direction (e.g., 'north' or 'n').")
        }

        return CommandResult(message)
    }
}
