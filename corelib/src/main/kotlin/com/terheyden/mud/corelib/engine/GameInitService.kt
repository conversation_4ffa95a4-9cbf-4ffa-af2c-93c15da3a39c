package com.terheyden.mud.corelib.engine

import com.terheyden.mud.corelib.GameContext
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.RoomRegistry

object GameInitService {

    fun initWorld(
        rooms: RoomRegistry,
        gameContext: GameContext,
    ) {
        // Initialize all rooms. Rooms will initialize their contents.
        rooms.allRooms.forEach { it.onInit(gameContext) }
    }

    fun initPlayer(
        player: Player,
        gameContext: GameContext,
    ) {
        // Initialize the player. Player will initialize their inventory.
        player.onInit(gameContext)
    }
}
