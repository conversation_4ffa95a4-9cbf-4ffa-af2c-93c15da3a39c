package com.terheyden.mud.corelib

/**
 * Interface for objects that have a unique ID.
 * This is used for serialization and deserialization.
 *
 * Most everything else in the game, e.g. primary names, aliases, etc. can change.
 * But IDs are immutable and permanent.
 */
interface Identifiable {
    /**
     * The unique ID for this object.
     * It should contain no spaces, but underscores are allowed.
     * Uniqueness is not checked at the moment.
     */
    val id: String

    /**
     * Unique object instance number, assigned via [GameObject].
     * This is used to create a unique object ID.
     */
    val objectNum: Long

    /**
     * Unique object ID, combining [id] and [objectNum].
     * Example: "sword-123"
     */
    val objectId: String
}
