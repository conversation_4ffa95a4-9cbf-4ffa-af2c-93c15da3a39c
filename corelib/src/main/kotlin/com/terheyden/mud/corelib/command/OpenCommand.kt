package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.Visible
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.lock.Closeable
import org.springframework.stereotype.Component

/**
 * Command to open doors.
 */
@Component
class OpenCommand : Command {
    override val name = "open"
    override val aliases = emptyList<String>()
    override val description = "Open something like a door or a chest"

    override fun execute(player: Player, arg: String?): CommandResult {
        if (arg.isNullOrEmpty()) {
            val openableThings = findOpenableThings(player)

            if (openableThings.isNotEmpty()) {
                return CommandResult("You can open: ${openableThings.joinToString(", ") { it.name }}")
            }

            return CommandResult("You don't see anything here that can be opened.")
        }

        val objName = arg

        findOpenableThings(player).find { it.matches(objName) }?.let {
            return CommandResult((it as Closeable).open())
        }

        return CommandResult("There is no '$objName' here that can be opened.")
    }

    private fun findOpenableThings(player: Player): List<Visible> {
        val nearbyItems = GameCommands.findNearbyItems(player)
        return nearbyItems.filter { it is Closeable }
    }
}
