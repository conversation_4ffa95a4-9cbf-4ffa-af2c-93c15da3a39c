package com.terheyden.mud.corelib.tick

/**
 * Called by the scheduler every tick (1 second).
 * Handles ticks in the game world.
 */
interface TickService {

    /**
     * Register a tickable object.
     */
    fun register(tickable: Tickable)

    /**
     * Unregister a tickable object.
     */
    fun unregister(tickable: Tickable)

    fun scheduleAction(seconds: Int, action: () -> Unit)

    /**
     * Process a single tick.
     */
    fun tick()

    fun getTickStats(): TickStats

    /**
     * Clear all registered tickables.
     */
    fun clear()
}
