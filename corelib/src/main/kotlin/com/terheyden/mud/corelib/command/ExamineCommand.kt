package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to examine items in detail.
 */
@Component
class ExamineCommand : Command {
    override val name = "examine"
    override val aliases = listOf("exa", "x", "ex", "inspect")
    override val description = "Examine an item in detail"

    override fun execute(player: Player, arg: String?): CommandResult {
        if (arg.isNullOrEmpty()) {
            return CommandResult("Examine what? Specify an item to examine.")
        }

        val currentRoom = player.getCurrentRoom()
        val targetName = arg

        // Check items first (room, then inventory)
        val item = currentRoom.findItem(targetName) ?: player.findItem(targetName)
        if (item != null) {
            return CommandResult(item.getExamineDescription())
        }

        // Check room features
        val feature = currentRoom.findFeature(targetName)
        if (feature != null) {
            return CommandResult(feature.description)
        }

        return CommandResult("There is no '$targetName' here or in your inventory to examine.")
    }
}
