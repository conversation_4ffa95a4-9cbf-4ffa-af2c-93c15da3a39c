package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to show the player's inventory.
 */
@Component
class InventoryCommand : Command {
    override val name = "inventory"
    override val aliases = listOf("inv", "i")
    override val description = "Show your inventory"

    override fun execute(player: Player, arg: String?): CommandResult {
        return CommandResult(player.getInventoryDescription())
    }
}
