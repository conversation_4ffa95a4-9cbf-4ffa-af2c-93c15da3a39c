package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to lock doors with keys.
 */
@Component
class LockCommand : Command {
    override val name = "lock"
    override val aliases = listOf("close with")
    override val description = "Lock a door with a key"

    override fun execute(player: Player, arg: String?): CommandResult {
        if (arg.isNullOrEmpty()) {
            return CommandResult("Lock what? Specify a door to lock.")
        }

        val currentRoom = player.getCurrentRoom()

        // Parse arguments - expect "lock door with key" or "lock door key" or just "lock door"
        val parts = if (arg.contains(" with ")) {
            arg.split(" with ", limit = 2)
        } else {
            // Try to split by finding the last word as the key
            val words = arg.split(" ").toMutableList()

            if (words.size >= 2) {
                val keyName = words.removeLastOrNull() ?: ""
                listOf(words.joinToString(" "), keyName)
            } else {
                listOf(arg, "")
            }
        }

        val doorName = parts[0].trim()
        var keyName = if (parts.size > 1) parts[1].trim() else ""

        // Find the door in the room.
        val door = currentRoom.doors.values.find { it.matches(doorName) }
            ?: return CommandResult("There is no '$doorName' here that can be locked.")

        val requiredKeyId = door.requiredKeyId
        if (requiredKeyId == null) return CommandResult("The ${door.name} is not lockable.")

        // If no key specified, determine the key automatically
        if (keyName.isBlank()) {
            keyName = requiredKeyId
        }

        // Find the key in player's inventory
        val key = player.findItem(keyName)
        if (key == null) {
            return CommandResult("You don't have a '$keyName' in your inventory.")
        }

        // Attempt to lock the door
        return CommandResult(door.lock(key))
    }
}
