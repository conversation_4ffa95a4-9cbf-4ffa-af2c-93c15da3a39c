package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomRegistry
import org.springframework.stereotype.Component

/**
 * Command to move the player in a direction.
 */
@Component
class MoveCommand(
    private val roomRegistry: RoomRegistry,
) : Command {

    override val name = "move"
    override val aliases = listOf("go", "walk")
    override val description = "Move in a direction (north, south, east, west, etc.)"

    override fun execute(player: Player, arg: String?): CommandResult {
        val currentRoom = player.getCurrentRoom()

        // Determine the direction to move
        val direction = arg?.let {
            Direction.fromString(arg)
        } ?: return CommandResult("It looks like you can move: ${getMovableDirections(currentRoom)}")

        return CommandResult(GameCommands.move(player, direction, roomRegistry))
    }

    private fun getMovableDirections(room: Room): String {
        val availableExits = room.getAvailableExits()
        if (availableExits.isEmpty()) return "nowhere!"

        return availableExits.joinToString(", ") { it.name.lowercase() }
    }
}
