package com.terheyden.mud.corelib.event

import io.github.oshai.kotlinlogging.KotlinLogging
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.reflect.KClass

/**
 * An event bus for game events.
 *
 * Lives as a static service in corelib, because it has no depenencies, and is needed
 * by all modules.
 */
object EventService {

    private val logger = KotlinLogging.logger {}

    /**
     * Thread-safe map of event type to subscribers.
     *
     * Design choices for thread-safety:
     * - ConcurrentHashMap for safe concurrent access to the map of event classes.
     * - CopyOnWriteArrayList for the subscribers list to avoid ConcurrentModificationException
     *   when subscribers are added/removed while an event is being published.
     *   Iteration uses a snapshot, so handlers can safely subscribe/unsubscribe during publish.
     *
     * Characteristics:
     * - Publish is read-mostly: iteration over COWAL is safe and non-blocking.
     * - Subscribe/unsubscribe are writes and will copy the small list; lists are expected to be small
     *   in this game, so the trade-off favors simplicity and safety.
     */
    private val subscribers: ConcurrentHashMap<KClass<out GameEvent>, CopyOnWriteArrayList<(GameEvent) -> Unit>> =
        ConcurrentHashMap()

    @Suppress("UNCHECKED_CAST")
    fun <T : GameEvent> subscribe(eventClass: KClass<out T>, handler: (T) -> Unit): EventSubscription {
        val castHandler = handler as (GameEvent) -> Unit

        // Create the subscriber list if needed and add this handler.
        val handlers = subscribers.computeIfAbsent(eventClass) { CopyOnWriteArrayList() }
        handlers.add(castHandler)

        // Return an EventSubscription that can remove this handler safely.
        return EventSubscription {
            subscribers[eventClass]?.let { list ->
                list.remove(castHandler)
                if (list.isEmpty()) {
                    // Remove the key entirely when no subscribers remain.
                    subscribers.remove(eventClass, list)
                }
            }
        }
    }

    inline fun <reified T : GameEvent> subscribe(noinline handler: (T) -> Unit): EventSubscription {
        return subscribe(T::class, handler)
    }

    fun publish(event: GameEvent) {
        logger.debug { "Publishing event: ${event::class.simpleName}" }

        // CopyOnWriteArrayList allows safe iteration even if subscribers change during publish.
        subscribers[event::class]?.forEach { handler ->
            try {
                handler(event)
            } catch (ex: Exception) {
                // Isolate handler failures so one bad subscriber doesn't break the bus.
                logger.error(ex) { "Error handling event: ${event::class.simpleName}" }
            }
        }
    }
}
