package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.weapon.Weapon
import org.springframework.stereotype.Component

/**
 * Command to equip weapons and other equipment.
 */
@Component
class EquipCommand : Command {
    override val name = "equip"
    override val aliases = listOf("wield", "wear")
    override val description = "Equip a weapon or piece of equipment"

    override fun execute(player: Player, arg: String?): CommandResult {
        if (arg.isNullOrEmpty()) {
            return CommandResult("Equip what? Specify an item to equip.")
        }

        val itemName = arg

        val item = player.findItem(itemName)
            ?: return CommandResult("You don't have '$itemName' in your inventory.")

        // Check if it's a weapon
        if (item is Weapon) {
            return equipWeapon(player, item)
        }

        return CommandResult("You cannot equip the ${item.name}. It's not a weapon or piece of equipment.")
    }

    private fun equipWeapon(player: com.terheyden.mud.corelib.living.Player, weapon: Weapon): CommandResult {
        if (weapon.isBroken()) {
            return CommandResult("The ${weapon.name} is broken and cannot be equipped!")
        }

        val previousWeapon = player.equippedWeapon
        val success = player.equipWeapon(weapon)

        if (!success) {
            return CommandResult("You cannot equip the ${weapon.name}.")
        }

        return if (previousWeapon != null) {
            CommandResult("You unequip the ${previousWeapon.name} and equip the ${weapon.name}.")
        } else {
            CommandResult("You equip the ${weapon.name}.")
        }
    }
}
