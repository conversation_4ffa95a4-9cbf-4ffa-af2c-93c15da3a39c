package com.terheyden.mud.corelib

import com.terheyden.mud.corelib.tick.Tickable
import java.util.concurrent.atomic.AtomicLong

/**
 * Base class for all game objects.
 */
open class GameObject(
    override val id: String,
) : Identifiable, Initializable {

    /** Unique object instance number. */
    override val objectNum = nextUid()

    /** Unique object ID. */
    override val objectId = "$id-$objectNum"

    lateinit var game: GameContext

    companion object {

        private var uid = AtomicLong(1)

        fun nextUid() = uid.getAndIncrement()
    }

    override fun onInit(gameContext: GameContext) {
        this.game = gameContext
        game.objectService.registerGameObject(this)

        if (this is Tickable) {
            game.tickService.register(this)
        }
    }

    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as GameObject

        return objectId == other.objectId
    }

    override fun hashCode() = objectId.hashCode()
    override fun toString() = "GameObject(objectId='$objectId')"
}
