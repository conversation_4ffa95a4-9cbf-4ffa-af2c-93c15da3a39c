package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to show the player's current status including health, level, and equipment.
 */
@Component
class StatusCommand : Command {
    override val name = "status"
    override val aliases = listOf("stats", "st", "health", "hp")
    override val description = "Show your current health, level, and combat statistics"

    override fun execute(player: Player, arg: String?): CommandResult {
        return CommandResult(player.getCombatStatus())
    }
}
