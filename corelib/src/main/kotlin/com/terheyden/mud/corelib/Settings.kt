package com.terheyden.mud.corelib

import com.terheyden.mud.corelib.event.EventService
import com.terheyden.mud.corelib.event.GameEvent

/**
 * Managed way to store and retrieve game settings.
 */
open class Settings(
    private val id: String,
    private val settings: MutableMap<String, Any> = mutableMapOf(),
) : Map<String, Any> by settings {

    /**
     * Set a setting to a new value.
     * Publishes a [SettingChangedEvent] to notify listeners.
     */
    fun set(key: String, value: Any) {
        val oldValue = settings[key]
        settings[key] = value
        EventService.publish(SettingChangedEvent(id, key, value, oldValue))
    }

    override fun toString() = "Settings($settings)"

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as Settings
        return settings == other.settings
    }

    override fun hashCode(): Int {
        return settings.hashCode()
    }

    data class SettingChangedEvent(
        val settingsId: String,
        val settingName: String,
        val newValue: Any,
        val oldValue: Any?,
    ) : GameEvent
}
