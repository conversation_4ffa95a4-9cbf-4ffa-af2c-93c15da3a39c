package com.terheyden.mud.corelib.room

import com.terheyden.mud.corelib.Container
import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.GameContext
import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.living.Dialogue
import com.terheyden.mud.corelib.living.Living
import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.tick.Tickable
import kotlin.reflect.KClass

/**
 * Base class for all rooms in the game world.
 * Extends Container so rooms can hold items.
 */
abstract class Room(
    id: String,
    name: String,
    description: String,
    features: MutableList<RoomFeature> = mutableListOf(),
    val exits: MutableMap<Direction, KClass<out Room>>,
    doors: MutableList<Door> = mutableListOf(),
    items: MutableList<Item> = mutableListOf(),
) : Container(
    id = "room:$id",
    name = name,
    description = description,
    weight = 0,
    isPickupable = false,
    items = items,
), Tickable {

    val features = RoomFeatureManager(features.toMutableList())
    val doors: Map<Direction, Door> = doors.associateBy { it.direction }

    /**
     * Get the room ID that this room connects to in the given direction.
     */
    fun findExit(direction: Direction): KClass<out Room>? = exits[direction]

    /**
     * Get all available exits from this room.
     */
    fun getAvailableExits(): Set<Direction> = exits.keys

    fun addExit(direction: Direction, exitRoom: KClass<out Room>) {
        WordUti
        exits[direction] = exitRoom
    }

    fun addExit(direction: Direction, exitRoom: Room) {
        addExit(direction, exitRoom::class)
    }

    /**
     * Get a formatted string of available exits.
     */
    fun getExitsDescription(): String {

        val allExits = getAvailableExits()
        if (allExits.isEmpty()) return "There are no obvious exits."

        val exitNames = allExits.map {
            doors[it]?.getExitDescription() ?: it.name.lowercase()
        }

        return "*Exits:* ${exitNames.joinToString(", ")}"
    }

    /**
     * Find a feature in this room.
     */
    fun findFeature(featureName: String) = features.findFeature(featureName)

    /**
     * Check if movement in a direction is blocked by a door.
     * @return A message if movement is blocked, null otherwise
     */
    fun isMovementBlocked(direction: Direction): String? {
        // First check if there's even an exit in that direction.
        if (!exits.containsKey(direction)) return "There is no exit in that direction."

        // Then check if there's a door blocking the exit.
        val door = doors[direction] ?: return null

        return when {
            door.locked -> "The ${door.name} is locked. You need to unlock it first."
            door.closed -> "The ${door.name} is closed. You need to open it first."
            else -> null
        }
    }

    val contentsNonLiving: List<Item> get() = <EMAIL> { it is Living }
    val contentsNPCs: List<NPC> get() = <EMAIL><NPC>()

    /**
     * Get a description of NPCs in the room.
     */
    fun getNPCsDescription(): String {
        val npcs = contentsNPCs
        return if (npcs.isEmpty()) {
            ""
        } else {
            "*Present:* ${npcs.joinToString(", ") { it.name }}"
        }
    }

    /**
     * Get a description of items (excluding NPCs) in the room.
     */
    fun getItemsDescription(): String {
        val nonNpcItems = contentsNonLiving
        return if (nonNpcItems.isEmpty()) {
            ""
        } else {
            "*Items:* ${nonNpcItems.joinToString(", ") { it.getShortDescription() }}"
        }
    }

    /**
     * Get a description that includes items and optionally examinable features.
     */
    fun getFullDescription(showExaminableHint: Boolean = false): String {
        return buildString {
            appendLine("=== _${name}_ ===")
            appendLine()
            appendLine(description)
            appendLine()

            // Show NPCs separately from items
            val npcsDesc = getNPCsDescription()
            if (npcsDesc.isNotEmpty()) {
                appendLine(npcsDesc)
                appendLine()
            }

            // Show items (excluding NPCs)
            val itemsDesc = getItemsDescription()
            if (itemsDesc.isNotEmpty()) {
                appendLine(itemsDesc)
                appendLine()
            }

            if (showExaminableHint && !features.isEmpty()) {
                val examinableThings = features.getExaminableThings()
                if (examinableThings.isNotEmpty()) {
                    appendLine("*You can examine:* ${examinableThings.joinToString(", ")}")
                    appendLine()
                }
            }

            appendLine(getExitsDescription())
        }
    }

    /**
     * Called when a player enters this room.
     * Override to add custom behavior.
     */
    open fun onPlayerEnter(playerId: String) {
        // Default implementation does nothing
    }

    /**
     * Handle NPC reactions when a player enters the room.
     */
    fun handlePlayerEnter(player: Player) {
        val npcs = contentsNPCs
        if (npcs.isEmpty()) return

        npcs.forEach { npc ->
            val reaction = npc.onPlayerEnter(player)
            if (reaction != null) {
                npc.sayWithDelay(Dialogue(1, reaction))
            }
        }
    }

    /**
     * Called when a player leaves this room.
     * Override to add custom behavior.
     */
    open fun onPlayerLeave(playerId: String) {
        // Default implementation does nothing
    }

    override fun onTick(tickCount: Long) {
        // Default implementation does nothing
    }

    override fun onInit(gameContext: GameContext) {
        super.onInit(gameContext)
        doors.values.forEach {
            // TODO it.currentEnv = this
            it.onInit(gameContext)
        }

        // Our Container base class will take care of initializing our items.
    }
}
