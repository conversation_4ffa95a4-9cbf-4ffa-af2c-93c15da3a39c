package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to drop items from inventory to the current room.
 */
@Component
class DropCommand : Command {
    override val name = "drop"
    override val aliases = listOf("put", "place")
    override val description = "Drop an item from your inventory"

    override fun execute(player: Player, arg: String?): CommandResult {
        if (arg.isNullOrEmpty()) {
            return CommandResult("Drop what? Specify an item to drop.")
        }

        val currentRoom = player.getCurrentRoom()
        val itemName = arg

        val item = player.removeItem(itemName)
            ?: return CommandResult("You don't have '$itemName' in your inventory.")

        // Add to current room
        currentRoom.addItem(item)

        return CommandResult("You drop the ${item.name}.")
    }
}
