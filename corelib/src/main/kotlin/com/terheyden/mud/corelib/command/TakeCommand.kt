package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to take/pick up items from the current room.
 */
@Component
class TakeCommand : Command {
    override val name = "take"
    override val aliases = listOf("get", "pick", "pickup")
    override val description = "Take an item from the current room"

    override fun execute(player: Player, arg: String?): CommandResult {
        if (arg.isNullOrEmpty()) {
            return showTakeableItems(player)
        }

        val currentRoom = player.getCurrentRoom()
        val itemName = arg

        if (itemName == "all") {
            return CommandResult(takeAll(player))
        }

        val item = currentRoom.findItem(itemName)

        // Maybe they're talking about a room feature.
        if (item == null) {
            val feature = currentRoom.findFeature(itemName)
            if (feature != null) {
                return CommandResult("You can't take the ${feature.primaryName}.")
            }

            return CommandResult("There is no '$itemName' here.")
        }

        if (!item.isPickupable) {
            return CommandResult("You can't take the ${item.name}.")
        }

        if (!player.canAddItem(item)) {
            return CommandResult(
                "You can't carry the ${item.name}. It's too heavy! " +
                        "Your current weight: ${player.weightOfItems}/${player.maxWeight}",
            )
        }

        // Remove from room and add to player inventory
        player.addItem(item)
        return CommandResult("You take the ${item.name}.")
    }

    private fun showTakeableItems(player: Player): CommandResult {
        val takeableThings = findTakeableThings(player)

        if (takeableThings.isEmpty()) {
            return CommandResult("There is nothing here you can take.")
        }

        return CommandResult(
            """
                    You see some nearby things you can take: ${takeableThings.joinToString(", ") { it.name }}

                    Or you could try 'take all'.
                """.trimIndent(),
        )
    }

    private fun findTakeableThings(player: Player): List<Item> {
        return GameCommands.findNearbyItems(
            player,
            includeDoors = false,
            includePlayerInventory = false,
            includePlayerInventoryContainers = false,
        ).filter { it.isPickupable }
    }

    private fun takeAll(player: Player) = buildString {
        val takeableThings = findTakeableThings(player)

        takeableThings.forEach { item ->
            player.addItem(item)
            appendLine("You take: ${item.name}")
        }
    }
}
