package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to attack NPCs in combat.
 */
@Component
class AttackCommand : Command {
    override val name = "attack"
    override val aliases = listOf("fight", "kill", "hit")
    override val description = "Attack an NPC or creature"

    override fun execute(player: Player, arg: String?): CommandResult {
        if (arg.isNullOrEmpty()) {
            return CommandResult("Attack what? Specify a target to attack.")
        }

        val currentRoom = player.getCurrentRoom()
        val targetName = arg

        // Find NPCs in the current room
        val npcsInRoom = currentRoom.contentsNPCs
        val target = npcsInRoom.find { it.matches(targetName) }

        if (target == null) {
            return CommandResult("There is no '$targetName' here to attack.")
        }

        if (target.isDead()) {
            return CommandResult("The ${target.name} is already dead.")
        }

        if (player.isDead()) {
            return CommandResult("You are dead and cannot attack!")
        }

        // Check if attacking a friendly NPC.
        if (target.npcType == NPCType.FRIENDLY) {
            return CommandResult("You cannot bring yourself to attack the friendly ${target.name}!")
        }

        // Perform combat round.
        return CommandResult(player.startAttack(target))
    }
}
