package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to close doors.
 */
@Component
class CloseCommand : Command {
    override val name = "close"
    override val aliases = listOf("shut")
    override val description = "Close a door"

    override fun execute(player: Player, arg: String?): CommandResult {
        if (arg.isNullOrEmpty()) {
            return CommandResult("Close what? Specify a door to close.")
        }

        val currentRoom = player.getCurrentRoom()
        val doorName = arg

        val door = currentRoom.doors.values
            .find { it.matches(doorName) }
            ?: return CommandResult("There is no '$doorName' here that can be closed.")

        // Attempt to close the door
        return CommandResult(door.close())
    }
}
