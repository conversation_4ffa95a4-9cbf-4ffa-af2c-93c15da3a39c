package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to use or activate items with special abilities.
 */
@Component
class UseCommand : Command {
    override val name = "use"
    override val aliases = listOf("activate", "read", "eat", "drink", "open", "light")
    override val description = "Use, activate, or interact with an item"

    override fun execute(player: Player, arg: String?): CommandResult {
        if (arg.isNullOrEmpty()) {
            val useableThings = findUseableThings(player)
            return CommandResult("Nearby things you can use: ${useableThings.joinToString(", ") { it.name }}")
        }

        val currentRoom = player.getCurrentRoom()
        val itemName = arg

        // First check player's inventory
        val inventoryItem = player.findItem(itemName)
        if (inventoryItem != null) {
            return useItem(inventoryItem, player)
        }

        // Then check the current room
        val roomItem = currentRoom.findItem(itemName)
        if (roomItem != null) {
            return useItem(roomItem, player)
        }

        return CommandResult("You don't see '$itemName' here or in your inventory.")
    }

    private fun findUseableThings(player: Player): List<Item> {
        val currentRoom = player.getCurrentRoom()
        val nearbyItems = currentRoom.doors.values + currentRoom
        return nearbyItems.filter { it is Useable }
    }

    private fun useItem(item: Item, player: Player): CommandResult {
        // Check if the item is useable.
        if (item !is Useable) {
            return CommandResult("You can't use the ${item.name}. It doesn't have any special abilities.")
        }

        return CommandResult(item.use(player))
    }
}
