package com.terheyden.mud.corelib.room

import com.terheyden.mud.corelib.Direction
import kotlin.reflect.KClass
import org.springframework.stereotype.Component

/**
 * For managing room instances in the game world.
 */
@Component
class RoomRegistry(
    val allRooms: List<Room>,
    startRoom: StartingRoom,
) {

    val theVoid = TheVoid.Instance
    val startingRoom = startRoom.toRoom()

    private val roomsByClass: Map<KClass<out Room>, Room> = allRooms
        .toMutableList()
        .also { it.add(theVoid) }
        .associateBy { it::class }

    /**
     * Get a room instance by its class.
     */
    fun getRoom(roomClass: KClass<out Room>) = roomsByClass[roomClass]
        ?: throw IllegalArgumentException("Room class not found: ${roomClass.simpleName}")

    /**
     * Resolve a room exit - handles both string IDs and room classes.
     */
    fun resolveExit(fromRoom: Room, direction: Direction): Room? =
        fromRoom.findExit(direction)?.let { exitRoomClass ->
            getRoom(exitRoomClass)
        }
}
