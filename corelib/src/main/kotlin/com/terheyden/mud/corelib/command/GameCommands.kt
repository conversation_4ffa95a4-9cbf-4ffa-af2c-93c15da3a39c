package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomRegistry

/**
 * Actions that can take place in the game world.
 * Moving things, saying things, etc.
 */
object GameCommands {

    fun move(player: Player, direction: Direction, rooms: RoomRegistry): String {
        val currentRoom = player.getCurrentRoom()

        // Check if movement is blocked.
        currentRoom.isMovementBlocked(direction)?.let { blockMsg ->
            return blockMsg
        }

        // Use the room registry to resolve the exit (handles both room IDs and room classes)
        val destinationRoom = rooms.resolveExit(currentRoom, direction)
            ?: return "You can't go ${direction.lowercaseName} from here."

        // Move the player
        player.moveTo(destinationRoom)

        // Handle NPC reactions to player entering
        destinationRoom.handlePlayerEnter(player)

        return buildString {
            appendLine("You go ${direction.lowercaseName}.")
            appendLine()
            append(look(destinationRoom))
        }
    }

    fun look(room: Room) = buildString {
        append(room.getFullDescription())
    }

    fun findNearbyItems(
        player: Player,
        includeDoors: Boolean = true,
        includePlayerInventory: Boolean = true,
        includeRoomContents: Boolean = true,
        includeRoomContainers: Boolean = true,
        includePlayerInventoryContainers: Boolean = true,
    ): Set<Item> {
        val foundItems = mutableSetOf<Item>()
        val currentRoom = player.getCurrentRoom()

        if (includeDoors) {
            foundItems.addAll(currentRoom.doors.values)
        }

        if (includeRoomContents) {
            foundItems.addAll(currentRoom.items)
        }

        if (includeRoomContainers) {
            currentRoom.findAllContainers().forEach { container ->
                foundItems.addAll(container.items)
            }
        }

        if (includePlayerInventory) {
            foundItems.addAll(player.items)
        }

        if (includePlayerInventoryContainers) {
            player.findAllContainers().forEach { container ->
                foundItems.addAll(container.items)
            }
        }

        return foundItems
    }
}
