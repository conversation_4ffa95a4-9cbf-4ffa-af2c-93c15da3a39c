package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.living.Player
import org.springframework.stereotype.Component

/**
 * Command to look around the current room.
 */
@Component
class LookCommand : Command {
    override val name = "look"
    override val aliases = listOf("l")
    override val description = "Look around the current room"

    override fun execute(player: Player, arg: String?): CommandResult {
        val currentRoom = player.getCurrentRoom()

        val result = StringBuilder()
        result.append(currentRoom.getFullDescription())

        return CommandResult(result.toString())
    }
}
