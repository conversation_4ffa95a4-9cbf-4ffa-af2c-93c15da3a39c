package com.terheyden.mud.corelib.living

import com.terheyden.mud.corelib.event.EventService
import com.terheyden.mud.corelib.event.PlayerOutputEvent
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component

/**
 * Represents the player character in the game.
 */
@Component
class Player(
    id: String = "player",
    name: String = "Adventurer",
    description: String = "A brave adventurer.",
    var colorEnabled: Boolean = true,
) : Living(
    id = "player:$id",
    name = name,
    description = description,
) {

    private val logger = KotlinLogging.logger {}

    override fun hear(message: String) {
        logger.debug { "Player heard: $message" }
        EventService.publish(PlayerOutputEvent(message))
    }

    override fun onTick(tickCount: Long) {
        super.onTick(tickCount)

        // Attempt to continue combat if we're in the middle of a fight.
        continueCombat()
    }

    private fun continueCombat() {
        val target = isFighting as? NPC ?: return
        if (verifyCombat(target) != null) return

        val attack = startAttack(target)
        hear(attack)
    }
}
