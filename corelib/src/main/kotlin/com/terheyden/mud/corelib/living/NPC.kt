package com.terheyden.mud.corelib.living

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.TheVoid

/**
 * Represents a Non-Player Character (NPC) in the game world.
 */
abstract class NPC(
    id: String,
    name: String,
    description: String,
    currentRoom: Room = TheVoid.Companion.Instance,
    inventory: MutableList<Item> = mutableListOf(),
    maxInventoryWeight: Int = 50,
    maxHealthPoints: Int = 100,
    currentHealthPoints: Int = maxHealthPoints,
    experiencePoints: Int = 0,
    level: Int = 1,
    baseAttackPower: Int = 10,
    baseDefense: Int = 5,
    val npcType: NPCType = NPCType.NEUTRAL,
    val experienceReward: Int = level * 10,
    val lootTable: List<Item> = emptyList(),
) : Living(
    id = "npc:$id",
    name = name,
    description = description,
    currentRoom = currentRoom,
    inventory = inventory,
    maxInventoryWeight = maxInventoryWeight,
    maxHealthPoints = maxHealthPoints,
    currentHealthPoints = currentHealthPoints,
    experiencePoints = experiencePoints,
    level = level,
    baseAttackPower = baseAttackPower,
    baseDefense = baseDefense,
) {

    /**
     * Handle dialogue when a player talks to this NPC.
     */
    abstract fun handleDialogue(player: Player, message: String): String

    /**
     * Get the default greeting when a player first encounters this NPC.
     */
    abstract fun getGreeting(player: Player): String

    fun sayWithDelay(dialogue: Dialogue) {
        game.tickService.scheduleAction(dialogue.delaySecs) {
            say(dialogue.message)
        }
    }

    /**
     * Get the items this NPC drops when defeated.
     */
    fun getDroppedLoot(): List<Item> {
        // For now, return all loot. Later could add randomization.
        return lootTable.toList()
    }

    /**
     * Handle what happens when this NPC dies.
     */
    fun onDeath(killer: Living): String {
        return buildString {
            appendLine("$name has been killed by ${killer.name}!")

            val loot = getDroppedLoot()
            if (loot.isNotEmpty()) {
                appendLine("$name drops:")
                loot.forEach { item ->
                    getCurrentRoom().addItem(item)
                    appendLine("  - ${item.name}")
                }
            }

            if (experienceReward > 0) {
                val leveledUp = killer.gainExperience(experienceReward)
                appendLine("You gain $experienceReward experience points!")
                if (leveledUp) {
                    appendLine("Congratulations! You have reached level ${killer.level}!")
                }
            }
        }
    }

    /**
     * Get a combat action for this NPC during combat.
     * Override in specific NPC classes for custom behavior.
     */
    open fun getCombatAction(target: Living): String {
        return "attack" // Default action is to attack
    }

    /**
     * Handle NPC behavior when a player enters the room.
     */
    open fun onPlayerEnter(player: Player): String? {
        return when (npcType) {
            NPCType.AGGRESSIVE -> {
                "$name notices you and growls menacingly!"
            }

            NPCType.FRIENDLY -> {
                getGreeting(player)
            }

            NPCType.NEUTRAL -> null
        }
    }

    /**
     * Get a description that includes the NPC's current state.
     */
    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("$name appears to be in ${getHealthDescription()}.")

            when (npcType) {
                NPCType.FRIENDLY -> appendLine("They seem friendly and approachable.")
                NPCType.NEUTRAL -> appendLine("They seem indifferent to your presence.")
                NPCType.AGGRESSIVE -> appendLine("They look hostile and dangerous!")
            }

            if (equippedWeapon != null) {
                appendLine("They are wielding ${equippedWeapon!!.name}.")
            }
        }
    }
}

/**
 * Types of NPCs based on their behavior toward players.
 */
enum class NPCType(val displayName: String) {
    FRIENDLY("Friendly"),
    NEUTRAL("Neutral"),
    AGGRESSIVE("Aggressive")
}
