package com.terheyden.mud.corelib.testlib

import com.terheyden.mud.corelib.GameContext
import com.terheyden.mud.corelib.ObjectService
import com.terheyden.mud.corelib.command.ExamineCommand
import com.terheyden.mud.corelib.command.LookCommand
import com.terheyden.mud.corelib.command.MoveCommand
import com.terheyden.mud.corelib.command.OpenCommand
import com.terheyden.mud.corelib.command.TakeCommand
import com.terheyden.mud.corelib.command.UnlockCommand
import com.terheyden.mud.corelib.engine.CommandParser
import com.terheyden.mud.corelib.engine.GameEngine
import com.terheyden.mud.corelib.engine.GameInitService
import com.terheyden.mud.corelib.event.EventService
import com.terheyden.mud.corelib.event.PlayerOutputEvent
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.RoomRegistry
import com.terheyden.mud.corelib.tick.TickService
import io.github.oshai.kotlinlogging.KotlinLogging
import io.mockk.mockk
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.TestInstance.Lifecycle

@TestInstance(Lifecycle.PER_CLASS)
open class BaseTestlibTest {

    private val logger = KotlinLogging.logger {}

    protected lateinit var player: Player
    protected lateinit var rooms: RoomRegistry
    protected lateinit var engine: GameEngine
    protected lateinit var tickService: TickService
    val objectService: ObjectService = mockk(relaxed = true)
    protected lateinit var gameContext: GameContext

    /**
     * In the game driver, `ConsoleGameRunner` does the player setup.
     * In corelib, we do it here.
     */
    @BeforeAll
    fun setUp() {
        val startRoom = SouthRoom()
        player = Player()
        rooms = RoomRegistry(listOf(startRoom, NorthRoom()), startRoom)
        tickService = mockk<TickService>(relaxed = true)
        gameContext = GameContext(objectService, tickService)

        val commands = listOf(
            LookCommand(),
            MoveCommand(rooms),
            ExamineCommand(),
            TakeCommand(),
            UnlockCommand(),
            OpenCommand(),
        )

        engine = GameEngine(player, CommandParser(commands))

        // Initialize the game world.
        GameInitService.initWorld(rooms, gameContext)
        GameInitService.initPlayer(player, gameContext)

        // Do the setup piece that ConsoleGameRunner does.
        // Create player:
        player.apply {
            currentEnv = rooms.startingRoom
        }

        // Listen for output events to relay to the player
        EventService.subscribe<PlayerOutputEvent> { event ->
            println(event.output)
        }

        player.getCurrentRoom().handlePlayerEnter(player)
        logger.info { "Testlib initialized!" }
        logger.info { "Player created: ${player.name}" }
        logger.info { "Player current env: ${player.currentEnv!!.id}" }
    }

    fun processCommand(command: String): String {
        val result = engine.processCommand(command).message
        println(result)
        return result
    }
}
