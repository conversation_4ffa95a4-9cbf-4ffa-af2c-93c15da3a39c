package com.terheyden.mud.corelib

import com.terheyden.mud.corelib.testlib.TestKey
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class GameObjectTest {

    @Test
    fun `should have unique object IDs`() {
        val testKey = TestKey()
        val testKey2 = TestKey()
        assertThat(testKey.objectId).isNotEqualTo(testKey2.objectId)
        assertThat(testKey.objectId).contains(testKey.id)

        println(testKey.javaClass.name)
        println(testKey2.javaClass.name)
        println(getObjectShortName(testKey))
        println(getObjectShortName(testKey2 as GameObject))
    }

    fun getObjectShortName(obj: Any): String {
        // Get the obj name and package:
        val className = obj.javaClass.name

        // Return just the last two parts:
        val parts = className.split(".")
        return parts.takeLast(2).joinToString(".")
    }
}
