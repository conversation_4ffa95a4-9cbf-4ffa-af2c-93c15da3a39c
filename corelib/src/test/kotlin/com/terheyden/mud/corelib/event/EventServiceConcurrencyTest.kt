package com.terheyden.mud.corelib.event

import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

/**
 * Concurrency and robustness tests for EventService.
 * These tests use unique event types to avoid interference across tests.
 */
class EventServiceConcurrencyTest {

    data class ConcurrentTestEvent(val value: Int) : GameEvent
    data class ExceptionTestEvent(val message: String) : GameEvent

    @Test
    fun `should isolate handler exceptions and continue notifying others`() {
        val a = AtomicInteger(0)
        val c = AtomicInteger(0)

        val subA = EventService.subscribe(ExceptionTestEvent::class) { a.incrementAndGet() }
        val subB = EventService.subscribe(ExceptionTestEvent::class) { _ -> error("boom") }
        val subC = EventService.subscribe(ExceptionTestEvent::class) { c.incrementAndGet() }

        // Should not throw; failing handler must be isolated from others.
        EventService.publish(ExceptionTestEvent("test"))

        assertThat(a.get()).isEqualTo(1)
        assertThat(c.get()).isEqualTo(1)

        subA.unsubscribe(); subB.unsubscribe(); subC.unsubscribe()
    }

    @Test
    fun `should allow unsubscribe during publish without ConcurrentModificationException`() {
        val counter = AtomicInteger(0)

        var subscription: EventSubscription? = null
        val handler: (ConcurrentTestEvent) -> Unit = {
            counter.incrementAndGet()
            // Unsubscribe on first invocation while publish is iterating.
            subscription?.unsubscribe()
        }
        subscription = EventService.subscribe(ConcurrentTestEvent::class, handler)

        // First publish invokes and unsubscribes; second should not call the handler.
        EventService.publish(ConcurrentTestEvent(1))
        EventService.publish(ConcurrentTestEvent(2))

        assertThat(counter.get()).isEqualTo(1)
    }

    @Test
    fun `should be thread-safe under concurrent publish and subscribe`() {
        val hits = AtomicInteger(0)

        // A stable subscriber that stays registered across the entire test.
        val stableSub = EventService.subscribe(ConcurrentTestEvent::class) { hits.incrementAndGet() }

        val executor = Executors.newFixedThreadPool(8)

        // 1) Submit many concurrent publishes
        val publishes = 1_000
        repeat(publishes) { i ->
            executor.submit { EventService.publish(ConcurrentTestEvent(i)) }
        }

        // 2) Concurrently add and remove a flapping subscriber to stress internal structures
        val flaps = 200
        executor.submit {
            repeat(flaps) {
                val sub = EventService.subscribe(ConcurrentTestEvent::class) { /* no-op */ }
                // A tiny bit of churn
                sub.unsubscribe()
            }
        }

        executor.shutdown()
        executor.awaitTermination(10, TimeUnit.SECONDS)

        // The stable subscriber should have seen all events.
        assertThat(hits.get()).isEqualTo(publishes)

        stableSub.unsubscribe()
    }
}
