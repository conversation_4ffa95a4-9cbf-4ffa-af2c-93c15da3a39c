package com.terheyden.mud.corelib.room

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.testlib.TestKey
import io.github.oshai.kotlinlogging.KotlinLogging
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class DoorTest {

    private val logger = KotlinLogging.logger {}

    val lockedDoor1 = Door(
        id = "sturdy_door",
        name = "a sturdy door",
        aliases = listOf("door", "sturdy door"),
        description = "A sturdy door.",
        direction = Direction.NORTH,
        locked = true,
        requiredKeyId = "test_key",
    )

    val lockedDoor2 = Door(
        id = "sturdy_door",
        name = "a sturdy door",
        aliases = listOf("door", "sturdy door"),
        description = "A sturdy door.",
        direction = Direction.SOUTH,
        locked = true,
        requiredKeyId = "test_key",
    )

    val goodKey = TestKey()

    val wrongKey = Item(
        id = "wrong_key",
        name = "wrong key",
        description = "A wrong key",
    )

    @Test
    fun `cannot open locked door`() {
        assertThat(lockedDoor1.locked).isTrue()
        assertThat(lockedDoor1.closed).isTrue()

        val result = lockedDoor1.open()
        logger.info { "Open locked door result: $result" }

        assertThat(result).contains("locked")
        assertThat(lockedDoor1.locked).isTrue()
        assertThat(lockedDoor1.closed).isTrue()
    }

    @Test
    fun `can unlock door with correct key`() {
        assertThat(lockedDoor1.locked).isTrue()
        assertThat(lockedDoor1.closed).isTrue()

        val result = lockedDoor1.unlock(goodKey)
        logger.info { "Unlock locked door result: $result" }

        assertThat(result).contains("unlock")
        assertThat(lockedDoor1.locked).isFalse()
        assertThat(lockedDoor1.closed).isTrue()
    }

    @Test
    fun `can open door after unlocking`() {
        assertThat(lockedDoor1.locked).isTrue()
        assertThat(lockedDoor1.closed).isTrue()

        lockedDoor1.unlock(goodKey)
        val result = lockedDoor1.open()
        logger.info { "Open unlocked door result: $result" }

        assertThat(result).contains("open")
        assertThat(lockedDoor1.locked).isFalse()
        assertThat(lockedDoor1.closed).isFalse()
    }

    @Test
    fun `wrong key cannot unlock door`() {
        assertThat(lockedDoor1.locked).isTrue()
        assertThat(lockedDoor1.closed).isTrue()

        val result = lockedDoor1.unlock(wrongKey)
        logger.info { "Unlock locked door wrong key result: $result" }

        assertThat(result).contains("can't be unlocked")
        assertThat(lockedDoor1.locked).isTrue()
        assertThat(lockedDoor1.closed).isTrue()
    }

    @Test
    fun `door events are published`() {
        // Doors start locked and closed.
        assertThat(lockedDoor1.locked).isTrue()
        assertThat(lockedDoor2.locked).isTrue()
        assertThat(lockedDoor1.closed).isTrue()
        assertThat(lockedDoor2.closed).isTrue()

        lockedDoor1.unlock(goodKey)
        assertThat(lockedDoor1.locked).isFalse()
        assertThat(lockedDoor2.locked).isFalse()

        lockedDoor1.open()
        assertThat(lockedDoor1.closed).isFalse()
        assertThat(lockedDoor2.closed).isFalse()
    }
}
