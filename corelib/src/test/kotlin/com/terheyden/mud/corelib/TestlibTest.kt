package com.terheyden.mud.corelib

import com.terheyden.mud.corelib.testlib.BaseTestlibTest
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

/**
 * A test to verify the game world is properly initialized.
 */
class TestlibTest : BaseTestlibTest() {

    @Test
    fun `start room should have exits after initialization`() {
        // Get the starting room
        val startRoom = rooms.startingRoom
        assertNotNull(startRoom, "Start room should exist")

        // Check that it has exits
        val availableExits = startRoom.getAvailableExits()
        assertTrue(availableExits.isNotEmpty(), "Start room should have exits")

        assertThat(processCommand("north")).contains("unlock it first")
        processCommand("look")
        assertThat(processCommand("take key")).contains("You take")
        assertThat(processCommand("unlock door")).contains("You unlock")
        assertThat(processCommand("open door")).contains("You open")
        assertThat(processCommand("north"))
            .contains("circular") // room description
            .contains("(open)")   // verify the connected door is also open
    }
}
