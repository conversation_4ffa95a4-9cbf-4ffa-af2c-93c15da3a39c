plugins {
    kotlin("jvm")
    kotlin("plugin.spring")
    id("org.springframework.boot")
    id("io.spring.dependency-management")
    id("io.gitlab.arturbosch.detekt")

    // For importing JARs and docs into the IDE.
    idea
}

group = "com.terheyden"
version = "0.0.1-SNAPSHOT"

// Apply a specific Java toolchain to ease working on different environments.
java {
    toolchain {
        // Create JARs with sources and javadocs attached.
        withJavadocJar()
        withSourcesJar()

        // Set JDK version:
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Depend on corelib module
    implementation(project(":corelib"))

    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    developmentOnly("org.springframework.boot:spring-boot-devtools")
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit5")
    testImplementation("org.springframework.kafka:spring-kafka-test")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    // Kotlin logging API.
    implementation("io.github.oshai:kotlin-logging-jvm:7.0.5")

    // Use Mockk and SpringMockk instead of Mockito.
    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        // We don't need JUnit 4 support:
        exclude(module = "junit-vintage-driver")
        // Exclude Mockito, we use Mockk instead:
        exclude(module = "mockito-core")
    }
    // https://mvnrepository.com/artifact/com.ninja-squad/springmockk
    testImplementation("com.ninja-squad:springmockk:4.0.2")

    // Runtime dependency on mudlib for default game world
    runtimeOnly(project(":mudlib"))
}

kotlin {
    compilerOptions {
        freeCompilerArgs.addAll("-Xjsr305=strict")
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
}

// Download sources and docs for dependencies in IntelliJ IDEA.
idea {
    module {
        isDownloadJavadoc = true
        isDownloadSources = true
    }
}

// https://detekt.dev/docs/gettingstarted/gradle
detekt {
    toolVersion = "1.23.8"
    config.setFrom("../detekt-luke.yml")
    buildUponDefaultConfig = true
}
