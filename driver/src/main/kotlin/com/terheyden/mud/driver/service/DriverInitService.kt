package com.terheyden.mud.driver.service

import com.terheyden.mud.corelib.GameContext
import com.terheyden.mud.corelib.engine.GameInitService
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.RoomRegistry
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Service

/**
 * Initializes the player, rooms, and their contents once they are fully loaded.
 * Sets up and verifies state before the game starts.
 */
@Service
class DriverInitService(
    private val player: Player,
    private val rooms: RoomRegistry,
    private val gameContext: GameContext,
) : InitializingBean {

    private val logger = KotlinLogging.logger {}

    override fun afterPropertiesSet() {
        logger.info { "Initializing game world..." }
        GameInitService.initWorld(rooms, gameContext)
        GameInitService.initPlayer(player, gameContext)
        logger.info { "DriverInitService completed initialization!" }
    }
}
