package com.terheyden.mud.driver

import com.terheyden.mud.corelib.GameContext
import com.terheyden.mud.corelib.ObjectService
import com.terheyden.mud.corelib.tick.TickService
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class SpringMudConfig {

    @Bean
    fun gameContext(
        objectService: ObjectService,
        tickService: TickService,
    ) = GameContext(objectService, tickService)
}
