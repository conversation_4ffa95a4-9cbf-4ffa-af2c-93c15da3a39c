package com.terheyden.mud.driver.console

import com.terheyden.mud.corelib.command.GameCommands
import com.terheyden.mud.corelib.engine.GameEngine
import com.terheyden.mud.corelib.event.EventService
import com.terheyden.mud.corelib.event.PlayerOutputEvent
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.RoomRegistry
import com.terheyden.mud.driver.console.ColorFormatter.formatCommandResult
import com.terheyden.mud.driver.console.ConsoleIO.colorPrintln
import com.terheyden.mud.driver.console.ConsoleIO.print
import com.terheyden.mud.driver.console.ConsoleIO.println
import com.terheyden.mud.driver.service.DriverInitService
import io.github.oshai.kotlinlogging.KotlinLogging
import java.util.Scanner
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component

/**
 * Console-based game runner that handles the main game loop.
 * Only runs when not in test mode.
 *
 * ## Design Decisions:
 *
 * **Graceful Shutdown**: When the player quits, this runner properly shuts down
 * the Spring application context to ensure all background services (like the tick
 * system) are stopped cleanly. This prevents the application from hanging.
 */
@Component
@Profile("!test") // Don't run during tests
@ConditionalOnProperty(
    name = ["spring.main.web-application-type"],
    havingValue = "none",
    matchIfMissing = false,
)
class ConsoleGameRunner(
    private val gameEngine: GameEngine,
    private val player: Player,
    private val roomRegistry: RoomRegistry,
    private val driverInitService: DriverInitService,
    private val applicationContext: ApplicationContext,
) : CommandLineRunner {

    private val logger = KotlinLogging.logger {}
    private val scanner = Scanner(System.`in`)

    override fun run(vararg args: String?) {
        // Require some extra beans so we get run last and GameService is fully initialized:
        requireNotNull(player) { "Player not initialized." }
        requireNotNull(roomRegistry) { "RoomRegistry not initialized." }
        requireNotNull(driverInitService) { "DriverInitService not initialized." }
        requireNotNull(gameEngine) { "GameEngine not initialized." }

        logger.info { "Starting Spring MUD console game..." }

        try {
            startGame()
        } catch (e: Exception) {
            logger.error(e) { "Error running game" }
            println("An error occurred while running the game: ${e.message}")
        }
    }

    private fun startGame() {
        // Get player name
        println()
        println(gameEngine.getGameBanner())
        println()
        print("Enter your name: ")

        val playerName = try {
            scanner.nextLine().trim().ifEmpty { "Adventurer" }
        } catch (_: NoSuchElementException) {
            logger.warn { "No console input available, using default player name" }
            "Adventurer"
        }

        // Create player
        player.name = playerName
        player.currentEnv = roomRegistry.startingRoom

        // Listen for output events to relay to the player
        EventService.subscribe<PlayerOutputEvent> { event ->
            colorPrintln(player, event.output)
        }

        // Display welcome message
        println()
        colorPrintln(player, gameEngine.getWelcomeMessage(player))
        colorPrintln(player, GameCommands.look(player.getCurrentRoom()))
        player.getCurrentRoom().handlePlayerEnter(player)

        // Main game loop
        var shouldQuit = false
        while (!shouldQuit) {
            print(ColorFormatter.formatPrompt(player))
            val input = try {
                scanner.nextLine()
            } catch (_: NoSuchElementException) {
                logger.info { "No more input available, ending game" }
                break
            }

            if (input.isNotBlank()) {
                val result = gameEngine.processCommand(input)
                println()
                println(formatCommandResult(result.message, player))
                println()

                shouldQuit = result.shouldQuit
            }
        }

        logger.info { "Game ended for player: ${player.name}" }

        // Gracefully shut down the Spring application context
        // This ensures all background services (like TickServiceImpl) are properly stopped
        logger.info { "Shutting down Spring application..." }
        SpringApplication.exit(applicationContext, { 0 })
    }
}
