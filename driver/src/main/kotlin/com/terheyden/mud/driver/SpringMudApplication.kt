package com.terheyden.mud.driver

import com.terheyden.mud.driver.config.GameDriverProperties
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.runApplication

@SpringBootApplication(
    scanBasePackages = ["com.terheyden.mud"],
)
@EnableConfigurationProperties(GameDriverProperties::class)
class SpringMudApplication

@Suppress("SpreadOperator")
fun main(args: Array<String>) {
    // Disable web environment since this is a console application
    System.setProperty("spring.main.web-application-type", "none")
    runApplication<SpringMudApplication>(*args)
}
