package com.terheyden.mud.driver.console

import com.terheyden.mud.corelib.living.Player

/**
 * Support for console-based (command-line) I/O.
 */
object ConsoleIO {

    fun println(text: String? = null) =
        if (text == null) kotlin.io.println() else kotlin.io.println(text)

    fun print(text: String) = kotlin.io.print(text)

    fun colorPrintln(player: Player, text: String) = kotlin.io.println(
        ColorFormatter.formatText(
            player = player,
            text = text,
        ),
    )

    fun colorPrint(player: Player, text: String) = kotlin.io.print(
        ColorFormatter.formatText(
            player = player,
            text = text,
        ),
    )
}
