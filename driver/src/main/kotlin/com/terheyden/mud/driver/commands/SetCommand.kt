package com.terheyden.mud.driver.commands

import com.terheyden.mud.corelib.command.Command
import com.terheyden.mud.corelib.command.CommandResult
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.driver.logging.LoggingService
import org.springframework.stereotype.Component

/**
 * Command to manage player settings and system configuration.
 *
 * ## Design Decisions:
 *
 * **Unified Settings Interface**: Combines player preferences (color) with system
 * configuration (debug logging) in a single command for consistency and ease of use.
 *
 * **Runtime Debug Control**: Integrates with LoggingService to provide real-time
 * log level control during gameplay, essential for debugging live sessions.
 *
 * **Granular Control**: Supports both simple debug on/off and advanced logger-specific
 * level control for power users and developers.
 */
@Component
class SetCommand(
    private val loggingService: LoggingService,
) : Command {
    override val name = "set"
    override val aliases = listOf("setting", "config", "configure")
    override val description = "Manage player settings and system configuration"

    override fun execute(player: Player, arg: String?): CommandResult {

        if (arg.isNullOrEmpty()) {
            return showAllSettings(player)
        }

        val args = arg.split(" ")
        val setting = args[0].lowercase()

        return when (setting) {
            "color" -> handleColorSetting(player, args.drop(1))
            "debug" -> handleDebugSetting(args.drop(1))
            "log", "logging" -> handleLoggingSetting(args.drop(1))
            else -> CommandResult("Unknown setting '$setting'. Available settings: color, debug, log")
        }
    }

    /**
     * Show all current player settings.
     */
    private fun showAllSettings(player: Player): CommandResult {
        val colorStatus = if (player.colorEnabled) "*on*" else "_off_"
        val debugStatus = if (loggingService.isDebugEnabled()) "*on*" else "_off_"

        val message = buildString {
            appendLine("=== Your Settings ===")
            appendLine()
            appendLine("Color: $colorStatus")
            appendLine("Debug: $debugStatus")
            appendLine()
            appendLine("Use `set <setting> <value>` to change a setting.")
            appendLine("Examples:")
            appendLine("  `set color off` - Disable color output")
            appendLine("  `set debug on` - Enable debug logging")
            appendLine("  `set log com.terheyden.mud DEBUG` - Set specific logger level")
        }

        return CommandResult(message)
    }

    /**
     * Handle the color setting command.
     */
    private fun handleColorSetting(
        player: Player,
        args: List<String>,
    ): CommandResult {
        if (args.isEmpty()) {
            val status = if (player.colorEnabled) "*enabled*" else "_disabled_"
            return CommandResult("Color is currently $status. Use `set color on` or `set color off` to change it.")
        }

        val value = args[0].lowercase()

        return when (value) {
            "on", "true", "yes", "1" -> {
                player.colorEnabled = true
                CommandResult("Color has been *enabled*. Text decorations will now be displayed in color!")
            }

            "off", "false", "no", "0" -> {
                player.colorEnabled = false
                CommandResult("Color has been disabled. Text decorations will be shown as plain text.")
            }

            else -> {
                CommandResult("Invalid value '$value'. Use 'on' or 'off' for the color setting.")
            }
        }
    }

    /**
     * Handle the debug setting command.
     */
    private fun handleDebugSetting(args: List<String>): CommandResult {
        if (args.isEmpty()) {
            return CommandResult(loggingService.getLoggingStatus())
        }

        val value = args[0].lowercase()

        return when (value) {
            "on", "true", "yes", "1" -> {
                CommandResult(loggingService.enableDebugLogging())
            }

            "off", "false", "no", "0" -> {
                CommandResult(loggingService.disableDebugLogging())
            }

            "status" -> {
                CommandResult(loggingService.getLoggingStatus())
            }

            else -> {
                CommandResult("Invalid value '$value'. Use 'on', 'off', or 'status' for the debug setting.")
            }
        }
    }

    /**
     * Handle advanced logging configuration.
     */
    private fun handleLoggingSetting(args: List<String>): CommandResult {
        if (args.isEmpty()) {
            return CommandResult(loggingService.getLoggingStatus())
        }

        if (args.size < 2) {
            val availableLevels = loggingService.getAvailableLogLevels().joinToString(", ")
            return CommandResult(
                "Usage: `set log <logger-name> <level>`\n" +
                        "Available levels: $availableLevels\n" +
                        "Example: `set log com.terheyden.mud DEBUG`",
            )
        }

        val loggerName = args[0]
        val levelString = args[1]

        val level = loggingService.parseLogLevel(levelString)
        if (level == null) {
            val availableLevels = loggingService.getAvailableLogLevels().joinToString(", ")
            return CommandResult("Invalid log level '$levelString'. Available levels: $availableLevels")
        }

        return CommandResult(loggingService.setLogLevel(loggerName, level))
    }
}
