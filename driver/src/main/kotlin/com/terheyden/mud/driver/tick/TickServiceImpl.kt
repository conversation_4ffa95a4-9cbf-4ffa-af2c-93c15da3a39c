package com.terheyden.mud.driver.tick

import com.terheyden.mud.corelib.tick.TickAction
import com.terheyden.mud.corelib.tick.TickService
import com.terheyden.mud.corelib.tick.TickStats
import com.terheyden.mud.corelib.tick.Tickable
import com.terheyden.mud.driver.config.GameDriverProperties
import io.github.oshai.kotlinlogging.KotlinLogging
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.PriorityBlockingQueue
import java.util.concurrent.atomic.AtomicLong
import org.springframework.scheduling.annotation.Async
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

/**
 * Core tick service that drives the MUD's heartbeat system.
 * This service runs on a fixed schedule and updates all registered tickable objects.
 */
@Service
@EnableAsync
@EnableScheduling
class TickServiceImpl(
    private val gameDriverProperties: GameDriverProperties,
) : TickService {

    private val logger = KotlinLogging.logger {}
    private val lastTickCount = AtomicLong(0)
    private val lastTickTime = AtomicLong(0)

    /**
     * List of registered Tickable objects. CopyOnWriteArrayList gives us simple, iteration-safe
     * reads during the tick loop while allowing infrequent register/unregister writes.
     */
    private val tickables = CopyOnWriteArrayList<Tickable>()

    /**
     * Queue of scheduled actions ordered by the target tick. PriorityBlockingQueue provides
     * thread-safe peek/poll operations so producers can add actions while the tick loop consumes them.
     */
    private val tickActions = PriorityBlockingQueue<TickAction>()

    /**
     * Main tick method - called on schedule.
     * `@Async` prevents blocking the scheduler thread if tick processing
     * takes longer than expected, ensuring consistent tick intervals.
     */
    @Async
    @Scheduled(fixedRateString = "\${mud.tick.interval:1000}")
    override fun tick() {
        val tickCount = lastTickCount.incrementAndGet()

        // Update tickables:
        updateTickables(tickCount)

        // Execute any scheduled actions whose time has come:
        updateTickActions(tickCount)

        // Publish the new last tick:
        lastTickCount.set(tickCount)
        lastTickTime.set(System.currentTimeMillis())
    }

    private fun updateTickActions(currentTickCount: Long) {
        while (true) {
            val next = tickActions.peek() ?: break
            if (next.tickCount > currentTickCount) break

            // Remove and execute:
            tickActions.poll()?.let { action ->
                try {
                    action.action()
                } catch (ex: Exception) {
                    logger.error(ex) { "Scheduled tick action failed at tick: $currentTickCount" }
                }
            }
        }
    }

    private fun updateTickables(currentTickCount: Long) {
        tickables.forEach { tickable ->
            try {
                tickable.onTick(currentTickCount)
            } catch (ex: Exception) {
                // Isolate tickable failures so one bad actor doesn't stop the world.
                logger.error(ex) { "Tickable threw during onTick at tick: $currentTickCount" }
            }
        }
    }

    /**
     * Get tick statistics.
     */
    override fun getTickStats(): TickStats {
        return TickStats(
            currentTick = lastTickCount.get(),
            intervalMs = gameDriverProperties.mud.tick.interval,
        )
    }

    /**
     * Register a tickable object.
     */
    override fun register(tickable: Tickable) {
        tickables.add(tickable)
    }

    /**
     * Unregister a tickable object.
     */
    override fun unregister(tickable: Tickable) {
        tickables.remove(tickable)
    }

    override fun scheduleAction(seconds: Int, action: () -> Unit) {
        // Snapshot the last tick safely using the atomic reference.
        val baseTick = lastTickCount.get()
        val actionTickCount = baseTick + seconds.toLong()
        logger.debug { "Scheduling action for tick $actionTickCount" }
        tickActions.add(TickAction(actionTickCount, action))
    }

    /**
     * Clear all registered tickables.
     */
    override fun clear() {
        val tickableCount = tickables.size
        tickables.clear()
        logger.info { "Cleared $tickableCount tickables from registry" }
        val actionCount = tickActions.size
        tickActions.clear()
        logger.info { "Cleared $actionCount tick actions." }
    }
}
