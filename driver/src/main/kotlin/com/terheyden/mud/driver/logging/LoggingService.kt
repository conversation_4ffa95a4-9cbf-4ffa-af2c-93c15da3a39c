package com.terheyden.mud.driver.logging

import ch.qos.logback.classic.Level
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.LoggerContext
import io.github.oshai.kotlinlogging.KotlinLogging
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Service for managing application logging levels dynamically.
 * Allows changing log levels at runtime through in-game commands.
 *
 * ## Design Decisions:
 *
 * **Runtime Log Level Control**: Uses Logback's LoggerContext to dynamically
 * change log levels without restarting the application. This is essential for
 * debugging live game sessions and monitoring system performance.
 *
 * **Sensible Defaults**: Starts with WARN for root logger (minimal noise) and
 * INFO for MUD packages (important game events). Debug mode temporarily elevates
 * to DEBUG/INFO for detailed insights.
 *
 * **Package-based Control**: Separates MUD-specific logging from framework logging,
 * allowing developers to focus on game mechanics without being overwhelmed by
 * Spring/library debug output.
 *
 * **In-game Integration**: Designed to work seamlessly with the SetCommand for
 * real-time debugging during gameplay, making development and troubleshooting
 * much more efficient.
 */
@Service
class LoggingService {

    private val logger = KotlinLogging.logger {}
    private val loggerContext = LoggerFactory.getILoggerFactory() as LoggerContext

    // Default log levels
    private val defaultRootLevel = Level.WARN
    private val defaultMudLevel = Level.INFO

    init {
        // Set initial log levels
        setRootLogLevel(defaultRootLevel)
        setMudLogLevel(defaultMudLevel)
        logger.info { "LoggingService initialized with default levels: ROOT=$defaultRootLevel, MUD=$defaultMudLevel" }
    }

    /**
     * Enable debug logging (sets MUD packages to DEBUG, root to INFO).
     */
    fun enableDebugLogging(): String {
        setMudLogLevel(Level.DEBUG)
        setRootLogLevel(Level.INFO)
        logger.debug { "Debug logging enabled" }
        return "Debug logging *enabled*. MUD packages set to DEBUG level, root set to INFO level."
    }

    /**
     * Disable debug logging (revert to default levels).
     */
    fun disableDebugLogging(): String {
        setMudLogLevel(defaultMudLevel)
        setRootLogLevel(defaultRootLevel)
        logger.info { "Debug logging disabled" }
        return "Debug logging _disabled_. Reverted to default levels: MUD=INFO, root=WARN."
    }

    /**
     * Check if debug logging is currently enabled.
     */
    fun isDebugEnabled(): Boolean {
        val mudLogger = loggerContext.getLogger("com.terheyden.mud")
        return mudLogger.level == Level.DEBUG
    }

    /**
     * Get current logging status.
     */
    fun getLoggingStatus(): String {
        val rootLogger = loggerContext.getLogger(Logger.ROOT_LOGGER_NAME)
        val mudLogger = loggerContext.getLogger("com.terheyden.mud")

        return buildString {
            appendLine("=== Logging Status ===")
            appendLine()
            appendLine("Debug Mode: ${if (isDebugEnabled()) "*enabled*" else "_disabled_"}")
            appendLine("Root Level: `${rootLogger.level}`")
            appendLine("MUD Level: `${mudLogger.level}`")
            appendLine()
            appendLine("Debug mode shows detailed information about:")
            appendLine("  - Tick system processing")
            appendLine("  - NPC autonomous behavior")
            appendLine("  - Command execution")
            appendLine("  - Room transitions")
            appendLine("  - Combat calculations")
        }
    }

    /**
     * Set a specific log level for a logger.
     */
    fun setLogLevel(loggerName: String, level: Level): String {
        val targetLogger = loggerContext.getLogger(loggerName)
        val oldLevel = targetLogger.level
        targetLogger.level = level

        logger.info { "Changed log level for '$loggerName' from $oldLevel to $level" }
        return "Log level for `$loggerName` changed from `$oldLevel` to `$level`."
    }

    /**
     * Set the root logger level.
     */
    private fun setRootLogLevel(level: Level) {
        val rootLogger = loggerContext.getLogger(Logger.ROOT_LOGGER_NAME)
        rootLogger.level = level
    }

    /**
     * Set the MUD package logger level.
     */
    private fun setMudLogLevel(level: Level) {
        val mudLogger = loggerContext.getLogger("com.terheyden.mud")
        mudLogger.level = level
    }

    /**
     * Get available log levels.
     */
    fun getAvailableLogLevels(): List<String> {
        return listOf("TRACE", "DEBUG", "INFO", "WARN", "ERROR", "OFF")
    }

    /**
     * Parse a string to a Logback Level.
     * Returns null if the level string is invalid.
     */
    fun parseLogLevel(levelString: String): Level? {
        return try {
            Level.valueOf(levelString.uppercase())
        } catch (_: IllegalArgumentException) {
            logger.debug { "Invalid log level string: '$levelString'" }
            null
        }
    }

    /**
     * Validate that the logging service is properly initialized.
     * This method can be called to ensure the service is working correctly.
     */
    fun validateLoggingService(): String {
        return try {
            val rootLogger = loggerContext.getLogger(Logger.ROOT_LOGGER_NAME)
            val mudLogger = loggerContext.getLogger("com.terheyden.mud")

            if (rootLogger == null || mudLogger == null) {
                "ERROR: Logging service not properly initialized"
            } else {
                "Logging service is operational. Root: ${rootLogger.level}, MUD: ${mudLogger.level}"
            }
        } catch (e: Exception) {
            logger.error(e) { "Error validating logging service" }
            "ERROR: Failed to validate logging service: ${e.message}"
        }
    }
}
