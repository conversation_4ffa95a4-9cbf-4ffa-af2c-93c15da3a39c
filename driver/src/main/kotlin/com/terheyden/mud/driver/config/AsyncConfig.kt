package com.terheyden.mud.driver.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.task.TaskExecutor
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor

/**
 * Async executor configuration for @Async methods.
 *
 * We keep the pool small and bounded since the game loop is driven by a single scheduler
 * and tick processing should not fan out uncontrollably. If a tick takes too long, tasks will
 * queue up to a bounded capacity rather than spawning excessive threads.
 */
@Suppress("MagicNumber")
@Configuration
class AsyncConfig {

    @Bean("taskExecutor")
    fun taskExecutor(): TaskExecutor {
        return ThreadPoolTaskExecutor().apply {
            corePoolSize = 2
            maxPoolSize = 4
            setQueueCapacity(256)
            setThreadNamePrefix("async-")
            initialize()
        }
    }
}


