package com.terheyden.mud.driver.service

import com.terheyden.mud.corelib.GameObject
import com.terheyden.mud.corelib.ObjectService
import kotlin.reflect.KClass
import org.springframework.stereotype.Service

@Service
class ObjectServiceImpl : ObjectService {

    private val classMap = mutableMapOf<KClass<out GameObject>, MutableList<GameObject>>()
    private val idMap = mutableMapOf<String, MutableList<GameObject>>()

    override fun registerGameObject(gameObject: GameObject) {
        classMap.getOrPut(gameObject::class) { mutableListOf() }.add(gameObject)

        verifyIdClass(gameObject)
        idMap.getOrPut(gameObject.id) { mutableListOf() }.add(gameObject)
    }

    /**
     * Verify that the same ID is not used by two different classes.
     */
    private fun verifyIdClass(gameObject: GameObject) {
        val existingObj = idMap[gameObject.id]?.firstOrNull() ?: return
        require(existingObj::class == gameObject::class) {
            "ID ${gameObject.id} is already used by ${getObjectPackageId(existingObj)}, " +
                    "but is being used by ${getObjectPackageId(gameObject)}!"
        }
    }

    @Suppress("UNCHECKED_CAST")
    fun <T : GameObject> getGameObjects(gameObjectClass: KClass<out GameObject>): List<T> {
        return classMap[gameObjectClass] as List<T>? ?: emptyList()
    }

    fun getObjectPackageId(gameObject: GameObject): String =
        gameObject::class.qualifiedName
            ?: throw IllegalArgumentException("Class ${gameObject::class} has no qualified name!")
}
