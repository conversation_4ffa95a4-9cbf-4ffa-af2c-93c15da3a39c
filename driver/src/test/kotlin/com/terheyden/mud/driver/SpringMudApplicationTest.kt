package com.terheyden.mud.driver

import com.terheyden.mud.corelib.room.RoomRegistry
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

/**
 * A Spring Boot test to verify the game world is properly initialized.
 */
@SpringBootTest
@ActiveProfiles("test")
class SpringMudApplicationTest {

    @Autowired
    lateinit var rooms: RoomRegistry

    @Test
    fun `start room should have exits after initialization`() {
        // Get the starting room
        val startRoom = rooms.startingRoom
        assertNotNull(startRoom, "Start room should exist")

        // Check that it has exits
        val availableExits = startRoom.getAvailableExits()
        assertTrue(availableExits.isNotEmpty(), "Start room should have exits")
    }
}
