package com.terheyden.mud.driver

import com.terheyden.mud.corelib.engine.GameEngine
import com.terheyden.mud.corelib.event.EventService
import com.terheyden.mud.corelib.event.PlayerOutputEvent
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.RoomRegistry
import com.terheyden.mud.driver.console.ConsoleGameRunner
import com.terheyden.mud.driver.console.ConsoleIO.colorPrintln
import com.terheyden.mud.driver.service.DriverInitService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.InitializingBean
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component

/**
 * Does player setup for integration tests.
 * Corresponds to [ConsoleGameRunner] but for tests.
 */
@Component
@Profile("test")
class TestGameRunner(
    private val gameEngine: GameEngine,
    private val roomRegistry: RoomReg<PERSON>ry,
    private val player: Player,
    private val driverInitService: DriverInitService,
) : InitializingBean {

    private val logger = KotlinLogging.logger {}

    override fun afterPropertiesSet() {
        // Require some extra beans so we get run last:
        requireNotNull(driverInitService) { "DriverInitService not initialized." }

        // Do the setup piece that ConsoleGameRunner does.
        // Create player
        val player = player.apply {
            currentEnv = roomRegistry.startingRoom
        }

        // Listen for output events to relay to the player
        EventService.subscribe<PlayerOutputEvent> { event ->
            colorPrintln(player, event.output)
        }

        logger.info { "TestGameRunner initialized!" }
        logger.info { "Player created: ${player.name}" }
        logger.info { "Player current env: ${player.currentEnv!!.id}" }
        gameEngine.processCommand("look")
        player.getCurrentRoom().handlePlayerEnter(player)
    }
}
