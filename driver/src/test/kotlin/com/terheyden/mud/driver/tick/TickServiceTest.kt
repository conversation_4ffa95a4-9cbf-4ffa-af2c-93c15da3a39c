package com.terheyden.mud.driver.tick

import com.terheyden.mud.driver.config.GameDriverProperties
import java.util.concurrent.atomic.AtomicInteger
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class TickServiceTest {

    val tickService = TickServiceImpl(GameDriverProperties())

    @Test
    fun `should schedule actions`() {
        val counter = AtomicInteger(0)
        tickService.scheduleAction(1) { counter.incrementAndGet() }
        tickService.tick()

        assertThat(counter.get()).isEqualTo(1)

        tickService.tick()
        tickService.scheduleAction(2) { counter.incrementAndGet() }

        // Current sec is 2, action is scheduled in 2 more, so should run on tick 4.
        tickService.tick()
        assertThat(counter.get()).isEqualTo(1)

        tickService.tick()
        assertThat(counter.get()).isEqualTo(2)
    }
}
