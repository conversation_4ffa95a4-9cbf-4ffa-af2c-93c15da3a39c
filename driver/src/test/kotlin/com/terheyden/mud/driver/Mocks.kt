package com.terheyden.mud.driver

import com.terheyden.mud.driver.config.GameDriverProperties

object Mocks {

    fun createEngineProperties(
        tickInterval: Long = 1000,
        tickEnabled: Boolean = true,
        rootLogLevel: String = "WARN",
        mudLogLevel: String = "INFO",
    ) = GameDriverProperties(
        mud = GameDriverProperties.MudProperties(
            tick = GameDriverProperties.MudProperties.TickProperties(
                interval = tickInterval,
                enabled = tickEnabled,
            ),
        ),
        logging = GameDriverProperties.LoggingProperties(
            level = GameDriverProperties.LoggingProperties.LevelProperties(
                root = rootLogLevel,
                mud = mudLogLevel,
            ),
        ),
    )
}
