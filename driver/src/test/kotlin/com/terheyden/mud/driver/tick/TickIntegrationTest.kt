package com.terheyden.mud.driver.tick

import com.terheyden.mud.corelib.tick.TickService
import com.terheyden.mud.corelib.tick.Tickable
import java.util.concurrent.atomic.AtomicInteger
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource

/**
 * Integration test to verify the tick system works with the full Spring context.
 */
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(
    properties = [
        "mud.tick.enabled=true",
        "mud.tick.interval=100", // Fast ticks for testing
    ],
)
class TickIntegrationTest {

    @Autowired
    lateinit var tickService: TickService

    @Test
    fun testTickSystem() {
        // TickServiceImpl should be running and publishing ticks.
        // TickService subscribes to the tick event and starts with no Tickables.

        // Register a tickable that counts ticks.
        val counter = AtomicInteger(0)
        val testTickable = object : Tickable {
            override fun onTick(tickCount: Long) {
                counter.incrementAndGet()
            }
        }
        tickService.register(testTickable)

        // Wait for a few ticks to pass.
        Thread.sleep(250)

        // Verify the tickable was called.
        assertThat(counter.get()).isGreaterThan(0)

        // Unregister ourselves.
        tickService.unregister(testTickable)
        val prevCount = counter.get()

        // Wait for a few more ticks to pass.
        Thread.sleep(250)

        // Verify the tickable was not called again.
        assertThat(counter.get()).isEqualTo(prevCount)
    }
}
