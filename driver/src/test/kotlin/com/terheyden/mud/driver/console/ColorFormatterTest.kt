package com.terheyden.mud.driver.console

import com.terheyden.mud.corelib.living.Player
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ColorFormatterTest {

    private val player = Player()

    @Test
    fun `should apply color decorations when color is enabled`() {
        player.colorEnabled = true

        val input = "This is _bold yellow_ text with *bold white* and `bold blue` decorations."
        val result = ColorFormatter.formatText(player, input)

        // Should contain ANSI color codes
        assertTrue(result.contains(AnsiColors.BOLD_YELLOW))
        assertTrue(result.contains(AnsiColors.BOLD_WHITE))
        assertTrue(result.contains(AnsiColors.BOLD_CYAN))
        assertTrue(result.contains(AnsiColors.RESET))

        // Should not contain the original markdown
        assertFalse(result.contains("_bold yellow_"))
        assertFalse(result.contains("*bold white*"))
        assertFalse(result.contains("`bold blue`"))
    }

    @Test
    fun `should strip decorations when color is disabled`() {
        player.colorEnabled = false

        val input = "This is _bold yellow_ text with *bold white* and `bold blue` decorations."
        val result = ColorFormatter.formatText(player, input)

        // Should not contain ANSI color codes
        assertFalse(result.contains("\u001B["))

        // Should not contain markdown decorations
        assertFalse(result.contains("_bold yellow_"))
        assertFalse(result.contains("*bold white*"))
        assertFalse(result.contains("`bold blue`"))

        // Should contain the plain text
        assertTrue(result.contains("bold yellow"))
        assertTrue(result.contains("bold white"))
        assertTrue(result.contains("bold blue"))

        assertEquals("This is bold yellow text with bold white and bold blue decorations.", result)
    }

    @Test
    fun `should format prompt with color when enabled`() {
        player.colorEnabled = true

        val result = ColorFormatter.formatPrompt(player)

        assertTrue(result.contains("\u001B[1;32m")) // Bold green
        assertTrue(result.contains("> "))
        assertTrue(result.contains("\u001B[0m"))    // Reset
    }

    @Test
    fun `should format prompt without color when disabled`() {
        player.colorEnabled = false

        val result = ColorFormatter.formatPrompt(player)

        assertFalse(result.contains("\u001B["))
        assertEquals("> ", result)
    }
}
