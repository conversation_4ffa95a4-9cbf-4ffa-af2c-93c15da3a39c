package com.terheyden.mud.driver.tick

import com.terheyden.mud.corelib.tick.Tickable
import com.terheyden.mud.driver.config.GameDriverProperties
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

/**
 * Concurrency and robustness tests for TickService.
 * These tests exercise concurrent register/unregister and scheduleAction while ticks are processed.
 */
class TickServiceConcurrencyTest {

    val tickService = TickServiceImpl(GameDriverProperties())

    @Test
    fun `should allow register and unregister during tick`() {
        val hits = AtomicInteger(0)

        // A tickable that increments and unregisters itself during tick
        val selfRemoving = object : Tickable {
            override fun onTick(tickCount: Long) {
                hits.incrementAndGet()
                tickService.unregister(this)
            }
        }

        tickService.register(selfRemoving)

        // Run a few ticks; the tickable should only run once without causing exceptions.
        tickService.tick()
        tickService.tick()
        tickService.tick()

        assertThat(hits.get()).isEqualTo(1)
    }

    @Test
    fun `should be thread-safe under concurrent scheduleAction and tick`() {
        val executed = AtomicInteger(0)

        val exec = Executors.newFixedThreadPool(4)

        // Concurrently schedule actions relative to moving lastTickEvent
        repeat(500) {
            exec.submit { tickService.scheduleAction(1) { executed.incrementAndGet() } }
        }

        // Process ticks while scheduling is happening
        repeat(5) { exec.submit { tickService.tick() } }

        exec.shutdown()
        exec.awaitTermination(5, TimeUnit.SECONDS)

        // Some actions may be scheduled after a tick boundary; ensure none were lost due to concurrency
        // and at least most executed after enough ticks.
        tickService.tick()

        assertThat(executed.get()).isEqualTo(500)
    }

    @Test
    fun `tickable exceptions are isolated and do not stop other tickables`() {
        val okHits = AtomicInteger(0)

        val bad = object : Tickable {
            override fun onTick(tickCount: Long) {
                error("boom")
            }
        }
        val ok = object : Tickable {
            override fun onTick(tickCount: Long) {
                okHits.incrementAndGet()
            }
        }

        tickService.register(bad)
        tickService.register(ok)

        tickService.tick()

        assertThat(okHits.get()).isEqualTo(1)
    }
}
