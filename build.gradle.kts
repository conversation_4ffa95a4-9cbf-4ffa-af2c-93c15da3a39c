// Root project configuration for the multi-module Spring MUD
// This project is now organized into three modules:
// - corelib: Core game interfaces and base classes
// - driver: Spring Boot application, game driver, and commands
// - mudlib: Default game world content (rooms, items, etc.)

plugins {
    kotlin("jvm") version "2.0.21" apply false
    kotlin("plugin.spring") version "2.0.21" apply false
    id("org.springframework.boot") version "3.5.3" apply false
    id("io.spring.dependency-management") version "1.1.7" apply false
    // https://mvnrepository.com/artifact/io.gitlab.arturbosch.detekt/detekt-gradle-plugin
    id("io.gitlab.arturbosch.detekt") version "1.23.8" apply false
    idea
}

group = "com.terheyden"
version = "0.0.1-SNAPSHOT"

// Configure all subprojects
subprojects {
    repositories {
        mavenCentral()
    }
}
