# Debug Logging System Demo

## 🎯 **In-Game Debug Logging Commands**

The Spring MUD now supports dynamic log level control through in-game commands!

### **Available Commands**

#### **1. View Current Settings**

```
set
```

Shows all current settings including debug status.

#### **2. Debug Control**

```
set debug on          # Enable debug logging
set debug off         # Disable debug logging  
set debug status      # Show current logging status
```

#### **3. Advanced Logging Control**

```
set log com.terheyden.mud DEBUG        # Set MUD packages to DEBUG
set log com.terheyden.mud.driver TRACE # Set engine to TRACE level
set log root INFO                      # Set root logger to INFO
```

#### **4. System Monitoring**

```
tick                  # View tick system status
```

### **What You'll See with Debug Enabled**

When debug logging is enabled (`set debug on`), you'll see detailed information about:

- **Tick System Processing**: Every tick with active tickables
- **Command Execution**: Detailed command processing logs
- **NPC Autonomous Behavior**: Village Elder speech and action decisions
- **Room Transitions**: Movement and room message processing
- **Combat Calculations**: Detailed damage and experience calculations

### **Example Session**

```
> set
=== Your Settings ===

Color: on
Debug: off

> set debug on
Debug logging enabled. MUD packages set to DEBUG level, root set to INFO level.

> tick
=== Tick System Status ===

Status: Running
Current Tick: 42
Tick Interval: 1000ms
Registered Tickables: 1

> north
You go north.

=== Village Square ===
[... room description ...]

*Recent activity:*
  village elder says: "The old paths through the forest are not as safe as they once were..."

> set debug off
Debug logging disabled. Reverted to default levels: MUD=INFO, root=WARN.
```

### **Configuration**

Default log levels (in `application.yml`):

- **Root**: WARN (minimal output)
- **MUD packages**: INFO (important events only)

Debug mode changes:

- **Root**: INFO (more system information)
- **MUD packages**: DEBUG (detailed game mechanics)

### **Benefits**

- **Real-time debugging** without restarting the game
- **Performance monitoring** of the tick system
- **NPC behavior insights** for game balancing
- **Command execution tracing** for troubleshooting
- **Granular control** over specific logger levels

This system is perfect for:

- **Development**: Understanding game mechanics
- **Testing**: Monitoring system performance
- **Debugging**: Troubleshooting issues in real-time
- **Balancing**: Observing NPC behavior patterns
